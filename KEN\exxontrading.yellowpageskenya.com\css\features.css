.card,.features{overflow:hidden}.card h3,.text-content h2{font-size:20px;font-weight:600}*{margin:0;padding:0;box-sizing:border-box}body{font-family:'work sans',serif}.features {
  position: relative;
  padding: 120px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  overflow: hidden;
}
.pattern-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
  pointer-events: none;
}
.feature-container {
  position: relative;
  z-index: 2;
}
.features-content{position:relative;z-index:1}.grid-container{display:grid;grid-template-columns:100%;align-items:center}.text-content{padding-right:1rem}.text-content p{color:#111;line-height:1.4}.card h3,.card-icon{vertical-align:middle;color:#fff}.text-content h2{margin-bottom:1rem;background:linear-gradient(45deg,#212529,#212529);-webkit-background-clip:text;-webkit-text-fill-color:transparent;color:#fff}.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  margin-top: 60px;
}
.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px 32px;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}
.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #10b981, #8b5cf6);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}
.card:hover::before {
  transform: scaleX(1);
}
.card:hover {
  transform: translateY(-12px);
  box-shadow: 
    0 32px 64px -12px rgba(0, 0, 0, 0.15),
    0 20px 25px -5px rgba(0, 0, 0, 0.1);
}
.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}
.card-icon {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.card:hover .card-icon {
  background: linear-gradient(135deg, #10b981, #059669);
  transform: rotate(5deg) scale(1.1);
}
.card-icon svg {
  color: white;
  width: 28px;
  height: 28px;
}
.card h3 {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.025em;
}
.card p {
  color: #64748b;
  line-height: 1.7;
  font-size: 16px;
  margin: 0;
}
.custom-list {
  list-style: none;
  padding: 0;
  margin: 0;
  space-y: 12px;
}
.custom-list li {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}
.custom-list li:hover {
  background: rgba(59, 130, 246, 0.1);
  transform: translateX(8px);
}
.custom-list li span {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #1e293b;
}
.custom-list li svg {
  color: #10b981;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}
.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(16, 185, 129, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: 24px;
}
.card:hover .card-overlay {
  opacity: 1;
}
.animate-text{opacity:0;animation:.8s forwards fadeInUp;color:#fff}.animate-text-delay{opacity:0;animation:.8s .2s forwards fadeInUp}.custom-list li,.feature-content{font-size:1rem;color:#fff}.custom-list{list-style-type:none}.custom-list li{display:flex;align-items:center;margin-bottom:.5rem;gap:.5rem;line-height:1.4;align-items:flex-start}.custom-list svg{width:1rem;height:1rem;color:#fff;flex-shrink:0}.custom-list span{line-height:1.2;text-align:left}@keyframes fadeInUp{from{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@media (max-width:767px){.grid-container{grid-template-columns:1fr;text-align:center;gap:2rem}.text-content{padding-right:0;text-align:center}.cards-container{grid-template-columns:1fr;gap:1.5rem}.features{padding:1.2rem .1rem;margin-top:0}.card-header{justify-content:center}.custom-list{padding-left:90px}}@media (min-width:768px) and (max-width:1024px){.grid-container{grid-template-columns:1fr;gap:2rem}.cards-container{grid-template-columns:repeat(2,1fr);gap:1.5rem}.text-content{padding-right:0;text-align:center}}@media (max-width: 768px) {
  .features {
    padding: 80px 0;
  }
  
  .cards-container {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-top: 40px;
  }
  
  .card {
    padding: 32px 24px;
    border-radius: 20px;
  }
  
  .card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
  }
  
  .card-icon svg {
    width: 24px;
    height: 24px;
  }
  
  .card h3 {
    font-size: 20px;
  }
}
