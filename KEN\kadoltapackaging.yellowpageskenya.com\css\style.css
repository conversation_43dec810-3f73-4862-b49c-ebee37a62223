.top-bar,
header,
nav ul {
    display: flex
}

.btn-primary {
    color: #f8f9fa;
    background-color: #2835ac;
    border: none
}

nav ul li a:hover {
    color: #2835ac
}

.jumbotron,
.welcome-section,
header {
    position: relative
}

*,
::after,
::before {
    box-sizing: border-box
}

body {
    font-family: Poppins
}

.btn-primary:hover {
    background-color: #902b2d;
    border-color: #2835ac
}

.top-bar {
    padding: 10px 10px 10px 30px;
    justify-content: flex-start;
    align-items: center;
    color: #fff
}

.top-bar .contact-info {
    display: flex;
    align-items: center;
    margin-left: 20px
}

.top-bar .contact-info i {
    margin-right: 10px;
    color: #fff;
    font-size: 1rem
}

.top-bar .contact-info span {
    margin-right: 10px;
    font-size: 1.1rem
}

.top-bar .contact-info span a {
    color: #fff;
    text-decoration: none
}

header {
    background-color: #fff;
    color: #333;
    padding: 0 20px;
    justify-content: space-between;
    align-items: center;
    z-index: 999;
    height: 80px
}

.logo {
    width: 241px;
    height: auto
}

nav ul {
    list-style-type: none
}

nav ul li {
    margin-left: 20px
}

nav ul li a {
    font-family: inherit;
    font-weight: 700;
    color: #902b2d;
    text-decoration: none;
    transition: color .3s
}

.hamburger:focus {
    outline-color: transparent;
    outline-style: none
}

.hamburger {
    display: none;
    cursor: pointer
}

.hamburger .line {
    width: 25px;
    height: 3px;
    background-color: #902b2d;
    margin: 5px;
    transition: transform .3s
}

.welcome-section {
    overflow: hidden
}

.jumbotron {
    background-image: url(../img/header.webp);
    background-position: bottom center;
    background-size: cover;
    background-repeat: no-repeat;
    height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    margin: 0
}

.jumbotron::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .5);
    backdrop-filter: blur(5px);
    z-index: 1
}

.jumbotron .container {
    position: relative;
    z-index: 2
}

.jumbotron h1 {
    margin-top: 0;
    font-size: 3rem;
    line-height: 1.2;
    color: #fff;
    text-shadow: 2px 2px 2px rgba(0, 0, 0, .5)
}

.jumbotron .lead {
    font-size: 1.55rem;
    margin-bottom: 1.5rem
}

.jumbotron .btn {
    font-size: 1.125rem;
    padding: .75rem 1.5rem
}

#about {
    background-color: #f8f9fa
}

#about .text-primary {
    color: #902b2d !important;
    margin-top: 2.5rem
}

#about .lead {
    font-size: 1.1rem;
    line-height: 1.6
}

#about h2 {
    font-size: 2.5rem;
    font-weight: 700
}

.card {
    background-color: #f5f5f5;
    border-bottom: 5px solid #c53636;
    transition: transform .3s ease-in-out
}

#project .project-item,
.service-item {
    transition: transform .3s, box-shadow .3s
}

.card:hover {
    transform: translateY(-10px)
}

.card-title {
    font-size: 1.5rem;
    margin-bottom: 1rem
}

.card-body,
.card-text,
.email-sm,
i.fas,
ul li {
    font-size: 1rem
}

.card-text {
    line-height: 1.5
}

ul {
    padding-left: 0
}

ul li {
    list-style: none;
    display: flex;
    align-items: center
}

ul li i {
    color: #c53636;
    margin-right: .5rem
}

.card-body {
    font-family: inherit;
    color: #000;
    line-height: 1.8
}

#service {
    margin-top: -50px
}

.fas {
    font-size: 3.5rem;
    color: #902b2d
}

#project .project-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px
}

#project .project-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform .3s
}

#project .project-item:hover img {
    transform: scale(1.05)
}

#project .section-title {
    font-size: 2rem;
    font-weight: 700;
    letter-spacing: 2px
}

#project .text-center {
    font-family: inherit;
    font-size: 1.2rem
}

.section-title {
    margin-top: 2rem;
    font-size: 2rem;
    margin-bottom: 1rem;
    font-weight: 700
}

.service-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, .1)
}

.top-bar,
footer {
    background-color: #902b2d
}

.back-to-top {
    position: fixed;
    display: none;
    right: 30px;
    bottom: 30px;
    z-index: 999;
    animation: 1s infinite alternate action
}

@media (max-width:1200px) {
    .jumbotron {
        background-image: url(../img/header.webp)
    }

    .col-lg-4 {
        flex: 0 0 50%;
        max-width: 50%
    }

    #commitment .row {
        margin-top: 3rem;
        margin-bottom: 4rem
    }

    #about .col-lg-6 {
        margin-top: 5rem
    }

    #project .section-title,
    #service .col-12 {
        margin-top: -4rem
    }
}

@media (max-width:998px) {
    #about .text-primary {
        margin-top: -1rem
    }

    #project .section-title {
        margin-top: -4rem
    }

    #project .g-4 {
        margin-top: -3rem
    }

    #service .col-12 {
        margin-top: -2rem
    }
}

@media (min-width:768px) {
    .logo-img {
        padding-left: 20px
    }
}

@media (max-width:768px) {
    .jumbotron {
        background-image: url(../img/header.webp)
    }

    .jumbotron h1 {
        font-size: 2rem
    }

    .jumbotron .lead {
        font-size: 1rem
    }

    .jumbotron .btn {
        font-size: 1rem;
        padding: .5rem 1rem
    }

    .nav-links {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 80px;
        left: 0;
        width: 100%;
        background-color: #fff;
        padding: 20px;
        z-index: 999;
        box-shadow: 0 5px 10px rgba(0, 0, 0, .1)
    }

    .nav-links.show {
        display: flex
    }

    .nav-links li {
        margin: 10px 0
    }

    .hamburger {
        display: block
    }

    .hamburger.active .line:first-child {
        transform: rotate(-45deg) translate(-5px, 6px)
    }

    .hamburger.active .line:nth-child(2) {
        opacity: 0
    }

    .hamburger.active .line:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px)
    }

    .col-lg-4,
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%
    }

    .service-item {
        padding: 2rem
    }

    #about .col-lg-6 {
        margin-top: 5rem
    }

    #about .text-primary {
        margin-top: -3rem
    }
}

@media (max-width:600px) {
    .top-bar {
        flex-direction: column;
        align-items: flex-start;
        padding-left: .5rem
    }

    .contact-info {
        margin-left: 0;
        margin-bottom: 10px
    }
}

@media (max-width:576px) {

    .jumbotron .btn,
    .jumbotron .lead {
        font-size: .875rem
    }

    .jumbotron h1 {
        font-size: 1.75rem
    }

    .jumbotron .btn {
        padding: .5rem 1rem
    }

    #about h2 {
        font-size: 2rem
    }

    #about .lead {
        font-size: 1rem
    }

    .email-sm {
        font-size: .9rem
    }
}