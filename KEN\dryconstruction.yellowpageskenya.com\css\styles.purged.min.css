/*!
 * Bootstrap v4.5.3 (https://getbootstrap.com/)
 * Copyright 2011-2020 The Bootstrap Authors
 * Copyright 2011-2020 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */


*,
::after,
::before {
    box-sizing: border-box
}

html {
    font-family: sans-serif;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent
}

footer,
nav {
    display: block
}

body {
    margin: 0;
    font-family: Poppins, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #000;
    text-align: left;
    background-color: #fff
}

h1,
h3,
h5,
h6 {
    margin-top: 0;
    margin-bottom: .5rem
}

p {
    margin-top: 0;
    margin-bottom: 1rem
}

ul {
    margin-top: 0;
    margin-bottom: 1rem
}

ul ul {
    margin-bottom: 0
}

b {
    font-weight: bolder
}

small {
    font-size: 80%
}

a {
    color: #954a27;
    text-decoration: none;
    background-color: transparent
}

a:hover {
    color: #b33200;
    text-decoration: underline
}

a:not([href]):not([class]) {
    color: inherit;
    text-decoration: none
}

a:not([href]):not([class]):hover {
    color: inherit;
    text-decoration: none
}

img {
    vertical-align: middle;
    border-style: none
}

svg {
    overflow: hidden;
    vertical-align: middle
}

button {
    border-radius: 0
}

button:focus {
    outline: 1px dotted;
    outline: 5px auto -webkit-focus-ring-color
}

button {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit
}

button {
    overflow: visible
}

button {
    text-transform: none
}

[role=button] {
    cursor: pointer
}

[type=button],
button {
    -webkit-appearance: button
}

[type=button]:not(:disabled),
button:not(:disabled) {
    cursor: pointer
}

[type=button]::-moz-focus-inner,
button::-moz-focus-inner {
    padding: 0;
    border-style: none
}

progress {
    vertical-align: baseline
}

::-webkit-file-upload-button {
    font: inherit;
    -webkit-appearance: button
}

.h1,
.h3,
.h5,
.h6,
h1,
h3,
h5,
h6 {
    margin-bottom: .5rem;
    font-weight: 500;
    line-height: 1.2;
    color: #1f1f2e
}

.h1,
h1 {
    font-size: 2.5rem
}

@media (max-width:1200px) {

    .h1,
    h1 {
        font-size: calc(1.375rem + 1.5vw)
    }
}

.h3,
h3 {
    font-size: 1.75rem
}

@media (max-width:1200px) {

    .h3,
    h3 {
        font-size: calc(1.3rem + .6vw)
    }
}

.h5,
h5 {
    font-size: 1.25rem
}

.h6,
h6 {
    font-size: 1rem
}

.display-3 {
    font-size: 4.5rem;
    font-weight: 300;
    line-height: 1.2
}

@media (max-width:1200px) {
    .display-3 {
        font-size: calc(1.575rem + 3.9vw)
    }
}

.small,
small {
    font-size: 80%;
    font-weight: 400
}

.list-inline {
    padding-left: 0;
    list-style: none
}

.img-fluid {
    max-width: 100%;
    height: auto
}

.container,
.container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto
}

@media (min-width:576px) {
    .container {
        max-width: 540px
    }
}

@media (min-width:768px) {
    .container {
        max-width: 720px
    }
}

@media (min-width:992px) {
    .container {
        max-width: 960px
    }
}

@media (min-width:1200px) {
    .container {
        max-width: 1140px
    }
}

.row {
    display: flex;
    flex-wrap: wrap
}

.col-12,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-md-12,
.col-md-6 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px
}

.col-12 {
    flex: 0 0 100%;
    max-width: 100%
}

@media (min-width:768px) {
    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%
    }

    .col-md-12 {
        flex: 0 0 100%;
        max-width: 100%
    }
}

@media (min-width:992px) {
    .col-lg-3 {
        flex: 0 0 25%;
        max-width: 25%
    }

    .col-lg-4 {
        flex: 0 0 33.33333%;
        max-width: 33.33333%
    }

    .col-lg-5 {
        flex: 0 0 41.66667%;
        max-width: 41.66667%
    }

    .col-lg-6 {
        flex: 0 0 50%;
        max-width: 50%
    }

    .col-lg-7 {
        flex: 0 0 58.33333%;
        max-width: 58.33333%
    }
}

.btn {
    display: inline-block;
    font-weight: 400;
    color: #757575;
    text-align: center;
    vertical-align: middle;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .btn {
        transition: none
    }
}

.btn:hover {
    color: #757575;
    text-decoration: none
}

.btn:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(255, 72, 0, .25)
}

.btn:disabled {
    opacity: .65
}

.btn:not(:disabled):not(.disabled) {
    cursor: pointer
}

.btn-primary {
    color: #fff;
    background-color: #954a27;
    border-color: #954a27
}

.btn-primary:hover {
    color: #fff;
    background-color: #954a27;
    border-color: #cc3a00
}

.btn-primary:focus {
    color: #fff;
    background-color: #954a27;
    border-color: #cc3a00;
    box-shadow: 0 0 0 .2rem rgba(255, 99, 38, .5)
}

.btn-primary:disabled {
    color: #fff;
    background-color: #954a27;
    border-color: #954a27
}

.btn-primary:not(:disabled):not(.disabled).active,
.btn-primary:not(:disabled):not(.disabled):active {
    color: #fff;
    background-color: #cc3a00;
    border-color: #bf3600
}

.btn-primary:not(:disabled):not(.disabled).active:focus,
.btn-primary:not(:disabled):not(.disabled):active:focus {
    box-shadow: 0 0 0 .2rem rgba(255, 99, 38, .5)
}

.btn-lg {
    padding: .5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: 0
}

.collapse:not(.show) {
    display: none
}

.nav {
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}

.nav-link {
    display: block;
    padding: .5rem 1rem
}

.nav-link:focus,
.nav-link:hover {
    text-decoration: none
}

.navbar {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding: .5rem 1rem
}

.navbar .container,
.navbar .container-fluid {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between
}

.navbar-brand {
    display: inline-block;
    padding-top: .3125rem;
    padding-bottom: .3125rem;
    margin-right: 1rem;
    font-size: 1.25rem;
    line-height: inherit;
    white-space: nowrap
}

.navbar-brand:focus,
.navbar-brand:hover {
    text-decoration: none
}

.navbar-nav {
    display: flex;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}

.navbar-nav .nav-link {
    padding-right: 0;
    padding-left: 0
}

.navbar-collapse {
    flex-basis: 100%;
    flex-grow: 1;
    align-items: center
}

.navbar-toggler {
    padding: .25rem .75rem;
    font-size: 1.25rem;
    line-height: 1;
    background-color: transparent;
    border: 1px solid transparent
}

.navbar-toggler:focus,
.navbar-toggler:hover {
    text-decoration: none
}

.navbar-toggler-icon {
    display: inline-block;
    width: 1.5em;
    height: 1.5em;
    vertical-align: middle;
    content: "";
    background: no-repeat center center;
    background-size: 100% 100%
}

@media (max-width:991.98px) {

    .navbar-expand-lg>.container,
    .navbar-expand-lg>.container-fluid {
        padding-right: 0;
        padding-left: 0
    }
}

@media (min-width:992px) {
    .navbar-expand-lg {
        flex-flow: row nowrap;
        justify-content: flex-start
    }

    .navbar-expand-lg .navbar-nav {
        flex-direction: row
    }

    .navbar-expand-lg .navbar-nav .nav-link {
        padding-right: .5rem;
        padding-left: .5rem
    }

    .navbar-expand-lg>.container,
    .navbar-expand-lg>.container-fluid {
        flex-wrap: nowrap
    }

    .navbar-expand-lg .navbar-collapse {
        display: flex !important;
        flex-basis: auto
    }

    .navbar-expand-lg .navbar-toggler {
        display: none
    }
}

.navbar-light .navbar-brand {
    color: rgba(0, 0, 0, .9)
}

.navbar-light .navbar-brand:focus,
.navbar-light .navbar-brand:hover {
    color: rgba(0, 0, 0, .9)
}

.navbar-light .navbar-nav .nav-link {
    color: rgba(0, 0, 0, .5)
}

.navbar-light .navbar-nav .nav-link:focus,
.navbar-light .navbar-nav .nav-link:hover {
    color: rgba(0, 0, 0, .7)
}

.navbar-light .navbar-nav .active>.nav-link,
.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .nav-link.show,
.navbar-light .navbar-nav .show>.nav-link {
    color: rgba(0, 0, 0, .9)
}

.navbar-light .navbar-toggler {
    color: rgba(0, 0, 0, .5);
    border-color: rgba(0, 0, 0, .1)
}

.navbar-light .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e")
}

.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, .125)
}

.card-body {
    flex: 1 1 auto;
    min-height: 1px;
    padding: 1.25rem
}

.card-img-top {
    flex-shrink: 0;
    width: 100%;
    height: 300px;
}

.jumbotron {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
    background-color: #e9ecef
}

@media (min-width:576px) {
    .jumbotron {
        padding: 4rem 2rem
    }
}

.jumbotron-fluid {
    padding-right: 0;
    padding-left: 0
}

.progress {
    display: flex;
    height: 1rem;
    overflow: hidden;
    line-height: 0;
    font-size: .75rem;
    background-color: #e9ecef
}

.bg-secondary {
    background-color: #f2f2f4 !important
}

a.bg-secondary:focus,
a.bg-secondary:hover,
button.bg-secondary:focus,
button.bg-secondary:hover {
    background-color: #d6d6dd !important
}

.bg-light {
    background-color: #fff !important
}

a.bg-light:focus,
a.bg-light:hover,
button.bg-light:focus,
button.bg-light:hover {
    background-color: #e6e6e6 !important
}

.bg-dark {
    background-color: #1f1f2e !important
}

a.bg-dark:focus,
a.bg-dark:hover,
button.bg-dark:focus,
button.bg-dark:hover {
    background-color: #0a0a10 !important
}

.border-0 {
    border: 0 !important
}

.d-inline-flex {
    display: inline-flex !important
}

.align-items-center {
    align-items: center !important
}

.overflow-hidden {
    overflow: hidden !important
}

.position-relative {
    position: relative !important
}

.w-100 {
    width: 100% !important
}

.m-0 {
    margin: 0 !important
}

.mb-0 {
    margin-bottom: 0 !important
}

.mb-2 {
    margin-bottom: .5rem !important
}

.mb-4 {
    margin-bottom: 1.5rem !important
}

.mt-5,
.my-5 {
    margin-top: 3rem !important
}

.mb-5,
.my-5 {
    margin-bottom: 3rem !important
}

.p-0 {
    padding: 0 !important
}

.py-0 {
    padding-top: 0 !important
}

.py-0 {
    padding-bottom: 0 !important
}

.py-2 {
    padding-top: .5rem !important
}

.pb-2,
.py-2 {
    padding-bottom: .5rem !important
}

.pt-3 {
    padding-top: 1rem !important
}

.px-3 {
    padding-right: 1rem !important
}

.px-3 {
    padding-left: 1rem !important
}

.py-4 {
    padding-top: 1.5rem !important
}

.pb-4,
.py-4 {
    padding-bottom: 1.5rem !important
}

.pt-5,
.py-5 {
    padding-top: 3rem !important
}

.py-5 {
    padding-bottom: 3rem !important
}

.m-auto {
    margin: auto !important
}

@media (min-width:576px) {
    .px-sm-3 {
        padding-right: 1rem !important
    }

    .px-sm-3 {
        padding-left: 1rem !important
    }
}

@media (min-width:768px) {
    .px-md-5 {
        padding-right: 3rem !important
    }

    .px-md-5 {
        padding-left: 3rem !important
    }
}

@media (min-width:992px) {
    .mb-lg-0 {
        margin-bottom: 0 !important
    }

    .ml-lg-4 {
        margin-left: 1.5rem !important
    }

    .py-lg-0 {
        padding-top: 0 !important
    }

    .pb-lg-0,
    .py-lg-0 {
        padding-bottom: 0 !important
    }

    .px-lg-4 {
        padding-right: 1.5rem !important
    }

    .px-lg-4 {
        padding-left: 1.5rem !important
    }

    .px-lg-5 {
        padding-right: 3rem !important
    }

    .px-lg-5 {
        padding-left: 3rem !important
    }
}

.text-left {
    text-align: left !important
}

.text-center {
    text-align: center !important
}

@media (min-width:992px) {
    .text-lg-left {
        text-align: left !important
    }
}

.text-uppercase {
    text-transform: uppercase !important
}

.font-weight-bold {
    font-weight: 700 !important
}

.text-white {
    color: #fff !important
}

.text-primary {
    color: #1f1f2e !important
}

a.text-primary:focus,
a.text-primary:hover {
    color: #b33200 !important
}

@media print {

    *,
    ::after,
    ::before {
        text-shadow: none !important;
        box-shadow: none !important
    }

    a:not(.btn) {
        text-decoration: underline
    }

    img {
        page-break-inside: avoid
    }

    h3,
    p {
        orphans: 3;
        widows: 3
    }

    h3 {
        page-break-after: avoid
    }

    @page {
        size: a3
    }

    body {
        min-width: 992px !important
    }

    .container {
        min-width: 992px !important
    }

    .navbar {
        display: none
    }
}

h1 {
    font-weight: 800 !important
}

.text-primary1 {
    color: #fff
}

.font-weight-bold,
h3 {
    font-weight: 700 !important
}

h6 {
    font-weight: 500 !important
}

p {
    font-weight: 400 !important
}

.back-to-top {
    position: fixed;
    display: none;
    right: 30px;
    bottom: 30px;
    z-index: 11;
    animation: action 1s infinite alternate
}

@keyframes action {
    0% {
        transform: translateY(0)
    }

    100% {
        transform: translateY(-15px)
    }
}

.navbar-light .navbar-nav .nav-link {
    padding: 30px 15px;
    font-weight: 600;
    letter-spacing: 1px;
    color: #1f1f2e;
    outline: 0
}

.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .nav-link:hover {
    color: #fff;
    background: #954a27
}

@media (max-width:991.98px) {
    .navbar-light .navbar-nav .nav-link {
        padding: 10px 15px
    }
}

.jumbotron {
    background: linear-gradient(rgba(0, 0, 0, .5), rgba(0, 0, 0, .5)), url(../img/header.webp);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover
}

.team img {
    position: relative
}

.team .team-text {
    position: relative;
    width: 100%;
    height: 200px;
    bottom: 0;
    left: 0;
    border-bottom: 5px solid #954a27;
    padding: 10px
}

.logo {
    width: 100px;
    height: 100px
}

#team .row img {
    object-fit: cover;
    object-position: center;
}