<!DOCTYPE html>
<html lang="pt">

<head>
  <meta charset="utf-8">
  <title>Suinave Lda: Cultivando Sucesso para Fazendas, Animais de Estimação e Indústrias</title>
  <meta name="description"
    content="Suinave Lda: O seu parceiro pecuário. Rações, máquinas, alimentos e equipamentos de qualidade com suporte para uma experiência agrícola completa.">


  <meta name="keywords" content="Suinave Lda ">

  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <!-- Favicon -->
  <link rel="icon" href="./favicon/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="./favicon/apple-touch-icon.jpg">
  <link rel="icon" type="image/jpg" sizes="32x32" href="./favicon/favicon-32x32.jpg">
  <link rel="icon" type="image/jpg" sizes="16x16" href="./favicon/favicon-16x16.jpg">
  <link rel="manifest" href="./favicon/site.webmanifest.json">
  <!-- Robots -->
  <meta name="robots" content="index, follow">
  <!-- Site Published Date -->
  <meta property="article:published_time" content="2025-09-19">
  <!-- Google Verification -->
  <!-- <meta name="google-site-verification" content="Your Google Search Console Verification Code"> -->
   <meta name="google-site-verification" content="Nfa81_h3ktKoyM-OShPPIX_9lm-mxtEed74zlQB9Eog" />

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://suinavelda.paginasamarelas.cv">
  <meta property="og:title" content="Suinave Lda: Cultivando Sucesso para Fazendas, Animais de Estimação e Indústrias">
  <meta property="og:description"
    content="Suinave Lda: O seu parceiro pecuário. Rações, máquinas, alimentos e equipamentos de qualidade com suporte para uma experiência agrícola completa.">
  <meta property="og:image" content="https://suinavelda.paginasamarelas.cv/img/logo.webp">
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:site" content="@yellowpages238">
  <meta property="twitter:url" content="https://suinavelda.paginasamarelas.cv ">
  <meta property="twitter:title" content="Suinave Lda: Cultivando Sucesso para Fazendas, Animais de Estimação e Indústrias ">
  <meta property="twitter:description"
    content="Suinave Lda: O seu parceiro pecuário. Rações, máquinas, alimentos e equipamentos de qualidade com suporte para uma experiência agrícola completa.">
  <meta property="twitter:image" content="https://suinavelda.paginasamarelas.cv/img/logo.webp">
  <!-- Canonical URL -->
  <link rel="canonical" href="https://suinavelda.paginasamarelas.cv">
  <!-- Hreflang tags -->
  <link rel="alternate" hreflang="en" href="https://suinavelda.paginasamarelas.cv">
  <!-- Include more hreflang tags here if you have the website available in other languages -->
  <!-- Sitemap -->
  <link rel="sitemap" type="application/xml" title="Sitemap"
    href="https://suinavelda.paginasamarelas.cv/sitemap.xml">

  <!-- Preconnect to Google Maps APIs -->
  <link rel="preconnect" href="https://maps.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://maps.gstatic.com" crossorigin>


  <link rel="preload" as="image" href="./img/slider_1a_scale,w_1351.webp" fetchpriority="high">
  <link rel="preload" as="image" href="./img/slider_1a_scale,w_905.webp" fetchpriority="high">
  <link rel="preload" as="image" href="./img/slider_1a_scale,w_601.webp" fetchpriority="high">


  <!-- Internal CSS -->

  <link rel="stylesheet" href="css/features.css">
  <link rel="stylesheet" href="css/ots.css">
  <link rel="stylesheet" href="css/s2.css">
  <link rel="stylesheet" href="css/hero.css">
  <link rel="stylesheet" href="css/service-section.css">
  <link rel="stylesheet" href="css/mn.css">
  <link rel="stylesheet" href="css/about.css">
  <link rel="stylesheet" href="css/main.css">
  <link rel="stylesheet" href="css/services.css">
  <link rel="stylesheet" href="css/testimonial.css">
  <!-- <link rel="stylesheet" href="css/categories.css"> -->
   


  <link rel="alternate" hreflang="en" href="https://suinavelda.paginasamarelas.cv/">
  <link rel="alternate" hreflang="x-default" href="https://suinavelda.paginasamarelas.cv/">



<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-0MYZZW46X5"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-0MYZZW46X5');
</script>


  <style>
    html {
      scroll-behavior: smooth;
    }

    /* Inline critical font styles for Poppins */
    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 400;
      src: local('Poppins Regular'), local('Poppins-Regular'), url(https://fonts.gstatic.com/s/poppins/v15/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 700;
      src: local('Poppins Bold'), local('Poppins-Bold'), url(https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.woff2) format('woff2');
      font-display: swap;
    }

    /* Inline critical font styles for Work Sans */

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 400;
      src: local('Work Sans Regular'),
        local('WorkSans-Regular'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 600;
      src: local('Work Sans SemiBold'),
        local('WorkSans-SemiBold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 700;
      src: local('Work Sans Bold'),
        local('WorkSans-Bold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }


    body {
      font-family: 'Work Sans', sans-serif;
    }

    .mobile-menu {
      transition: transform 0.3s ease-in-out;
    }

    .mobile-menu.hidden {
      transform: translateX(-100%);
    }

    #top-bar {
      transition: transform 0.3s ease-out, opacity 0.3s ease-out;
    }

    #main-nav {
      transition: all 0.3s ease-out;
    }

    .sticky {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 50;
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  </style>

<script type="application/ld+json">


{
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "Organization",
      "@id": "https://suinavelda.paginasamarelas.cv/#organization",
      "name": "Suinave Lda",
      "url": "https://suinavelda.paginasamarelas.cv",
      "logo": "https://suinavelda.paginasamarelas.cv/img/logo.webp",
      "description": "Suinave Lda: O seu parceiro pecuário. Rações, máquinas, alimentos e equipamentos de qualidade com suporte para uma experiência agrícola completa.",
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "9914261",
        "contactType": "Customer Service",
        "email": "<EMAIL>",
        "availableLanguage": "Portuguese"
      },
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Achada Grande Frente",
        "addressCountry": "Cabo Verde"
      },
      "foundingDate": "2002",
      "sameAs": [
        "https://www.facebook.com/p/Suinave-Lda-100068013262536/",
        "https://api.whatsapp.com/send/?phone=9914261"
      ]
    },
    {
      "@type": "LocalBusiness",
      "@id": "https://suinavelda.paginasamarelas.cv/#localbusiness",
      "name": "Suinave Lda",
      "image": "https://suinavelda.paginasamarelas.cv/img/logo.webp",
      "url": "https://suinavelda.paginasamarelas.cv",
      "telephone": "9914261",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Achada Grande Frente",
        "addressCountry": "Cabo Verde"
      },
      "openingHours": [
        "Mo-Fr 08:00-17:00",
        "Sa 08:00-13:00"
      ],
      "priceRange": "$$",
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "14.917660999999999",
        "longitude": "-23.493944199999998"
      },
      "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Produtos Agrícolas e Pecuários",
        "itemListElement": [
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Product",
              "name": "Alimentos para animais",
              "description": "Os nossos produtos para alimentação animal são formulados para apoiar a saúde, o crescimento e a produtividade dos animais."
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Product",
              "name": "Maquinaria industrial",
              "description": "As nossas máquinas são construídas com a mais recente tecnologia para garantir durabilidade, fiabilidade e desempenho superior."
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Product",
              "name": "Alimentos para o gado",
              "description": "Para além de rações, fornecemos uma variedade de produtos alimentares adaptados ao consumo do gado."
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Product",
              "name": "Medicamentos para o gado",
              "description": "Todos os nossos produtos são provenientes de fabricantes conceituados e são testados quanto à sua eficácia e segurança."
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Product",
              "name": "Equipamento para gado",
              "description": "Concentramo-nos em fornecer produtos que melhoram o bem-estar dos animais e tornam as tarefas de gestão mais fáceis e mais eficientes."
            }
          }
        ]
      }
    },
    {
      "@type": "WebSite",
      "@id": "https://suinavelda.paginasamarelas.cv/#website",
      "url": "https://suinavelda.paginasamarelas.cv",
      "name": "Suinave Lda",
      "description": "Cultivando Sucesso para Fazendas, Animais de Estimação e Indústrias",
      "publisher": {
        "@id": "https://suinavelda.paginasamarelas.cv/#organization"
      },
      "inLanguage": "pt",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://suinavelda.paginasamarelas.cv/?s={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    },
    {
      "@type": "WebPage",
      "@id": "https://suinavelda.paginasamarelas.cv/#webpage",
      "url": "https://suinavelda.paginasamarelas.cv",
      "name": "Suinave Lda: Cultivando Sucesso para Fazendas, Animais de Estimação e Indústrias",
      "isPartOf": {
        "@id": "https://suinavelda.paginasamarelas.cv/#website"
      },
      "about": {
        "@id": "https://suinavelda.paginasamarelas.cv/#organization"
      },
      "datePublished": "2025-09-19",
      "description": "Suinave Lda: O seu parceiro pecuário. Rações, máquinas, alimentos e equipamentos de qualidade com suporte para uma experiência agrícola completa.",
      "inLanguage": "pt",
      "potentialAction": {
        "@type": "ReadAction",
        "target": [
          "https://suinavelda.paginasamarelas.cv"
        ]
      }
    },
    {
      "@type": "AboutPage",
      "@id": "https://suinavelda.paginasamarelas.cv/#about",
      "url": "https://suinavelda.paginasamarelas.cv/#about",
      "name": "Sobre a Suinave",
      "description": "A Suinave é uma empresa consagrada no mercado nacional, com 23 anos de experiência na produção e comercialização de ração animal.",
      "isPartOf": {
        "@id": "https://suinavelda.paginasamarelas.cv/#website"
      },
      "mainEntity": {
        "@type": "Organization",
        "name": "Suinave Lda",
        "description": "Empresa consagrada no mercado nacional, com 23 anos de experiência na produção e comercialização de ração animal, bem como na transformação e empacotamento de gêneros alimentícios.",
        "foundingDate": "2002",
        "numberOfEmployees": {
          "@type": "QuantitativeValue",
          "value": "10",
          "unitText": "employees"
        },
        "serviceArea": {
          "@type": "Country",
          "name": "Cabo Verde"
        },
        "hasOfferCatalog": {
          "@type": "OfferCatalog",
          "name": "Produtos e Serviços",
          "itemListElement": [
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Product",
                "name": "Ração Animal"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Product",
                "name": "Máquinas Agrícolas"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Empacotamento de Gêneros Alimentícios"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Product",
                "name": "Medicamentos para Gado"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Product",
                "name": "Equipamentos para Gado"
              }
            }
          ]
        },
        "knowsAbout": [
          "Agricultura",
          "Pecuária",
          "Ração Animal",
          "Máquinas Agrícolas",
          "Transformação de Alimentos"
        ],
        "mission": "Esforçamo-nos por melhorar a saúde e a produtividade do gado, fornecendo rações de primeira qualidade e maquinaria de ponta e oferecendo um apoio abrangente.",
        "vision": "Revolucionar a gestão pecuária com tecnologia de ponta e produtos de qualidade superior.",
        "values": [
          {
            "@type": "DefinedTerm",
            "name": "Qualidade",
            "description": "Compromisso com a excelência em todos os produtos e serviços oferecidos."
          },
          {
            "@type": "DefinedTerm",
            "name": "Inovação",
            "description": "Busca constante por novas tecnologias e métodos para melhorar a produtividade agrícola."
          },
          {
            "@type": "DefinedTerm",
            "name": "Integridade",
            "description": "Transparência e honestidade em todas as relações comerciais e com clientes."
          }
        ]
      }
    },
    {
      "@type": "Service",
      "name": "Empacotamento de géneros alimentícios",
      "description": "Empacotamento de géneros alimentícios (sal, milho, xerém) com tecnologia de ponta para garantir a qualidade e conservação dos produtos.",
      "provider": {
        "@type": "Organization",
        "name": "Suinave Lda"
      },
      "serviceType": "Food Packaging",
      "category": "Serviços Agrícolas"
    },
    {
      "@type": "Service",
      "name": "Produção e comercialização de ração animal",
      "description": "Produção e comercialização de ração animal de alta qualidade para melhorar a saúde e produtividade do gado.",
      "provider": {
        "@type": "Organization",
        "name": "Suinave Lda"
      },
      "serviceType": "Animal Feed Production",
      "category": "Serviços Agrícolas"
    },
    {
      "@type": "Product",
      "name": "Alimentos para animais",
      "image": "https://suinavelda.paginasamarelas.cv/img/Alimentos_para_animais.webp",
      "description": "Os nossos produtos para alimentação animal são formulados para apoiar a saúde, o crescimento e a produtividade dos animais, assegurando que recebem nutrientes equilibrados e essenciais.",
      "brand": {
        "@type": "Brand",
        "name": "Suinave Lda"
      },
      "category": "Alimentação Animal",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "CVE",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "Maquinaria industrial",
      "image": "https://suinavelda.paginasamarelas.cv/img/Maquinaria_industrial.webp",
      "description": "As nossas máquinas são construídas com a mais recente tecnologia para garantir durabilidade, fiabilidade e desempenho superior, ajudando-o a simplificar os seus processos e a obter resultados óptimos.",
      "brand": {
        "@type": "Brand",
        "name": "Suinave Lda"
      },
      "category": "Maquinaria Agrícola",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "CVE",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "Alimentos para o gado",
      "image": "https://suinavelda.paginasamarelas.cv/img/Alimentos_para_o_gado.webp",
      "description": "Para além de rações, fornecemos uma variedade de produtos alimentares adaptados ao consumo do gado.",
      "brand": {
        "@type": "Brand",
        "name": "Suinave Lda"
      },
      "category": "Alimentação Animal",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "CVE",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "Medicamentos para o gado",
      "image": "https://suinavelda.paginasamarelas.cv/img/Medicamentos.webp",
      "description": "Todos os nossos produtos são provenientes de fabricantes conceituados e são testados quanto à sua eficácia e segurança.",
      "brand": {
        "@type": "Brand",
        "name": "Suinave Lda"
      },
      "category": "Medicamentos Veterinários",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "CVE",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "Equipamento para gado",
      "image": "https://suinavelda.paginasamarelas.cv/img/Equipamento_para_gado.webp",
      "description": "Concentramo-nos em fornecer produtos que melhoram o bem-estar dos animais e tornam as tarefas de gestão mais fáceis e mais eficientes para os agricultores.",
      "brand": {
        "@type": "Brand",
        "name": "Suinave Lda"
      },
      "category": "Equipamento Agrícola",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "CVE",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "ContactPage",
      "@id": "https://suinavelda.paginasamarelas.cv/#contact",
      "url": "https://suinavelda.paginasamarelas.cv/#contact",
      "name": "Contacte-nos",
      "description": "Informações de contacto para Suinave Lda incluindo endereço físico, número de telefone, email e horário de funcionamento.",
      "isPartOf": {
        "@id": "https://suinavelda.paginasamarelas.cv/#website"
      },
      "mainEntity": {
        "@type": "ContactPoint",
        "telephone": "9914261",
        "contactType": "Customer Service",
        "email": "<EMAIL>",
        "availableLanguage": "Portuguese",
        "areaServed": "CV",
        "hoursAvailable": [
          {
            "@type": "OpeningHoursSpecification",
            "dayOfWeek": [
              "Monday",
              "Tuesday",
              "Wednesday",
              "Thursday",
              "Friday"
            ],
            "opens": "08:00",
            "closes": "17:00"
          },
          {
            "@type": "OpeningHoursSpecification",
            "dayOfWeek": "Saturday",
            "opens": "08:00",
            "closes": "13:00"
          }
        ]
      }
    },
    {
      "@type": "CollectionPage",
      "@id": "https://suinavelda.paginasamarelas.cv/#products",
      "url": "https://suinavelda.paginasamarelas.cv/#products",
      "name": "Produtos",
      "description": "Navegue pela nossa gama de produtos agrícolas e pecuários incluindo alimentos para animais, maquinaria industrial e equipamentos para gado.",
      "isPartOf": {
        "@id": "https://suinavelda.paginasamarelas.cv/#website"
      },
      "mainEntity": {
        "@type": "ItemList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Alimentos para animais",
            "url": "https://suinavelda.paginasamarelas.cv/#products"
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": "Maquinaria industrial",
            "url": "https://suinavelda.paginasamarelas.cv/#products"
          },
          {
            "@type": "ListItem",
            "position": 3,
            "name": "Alimentos para o gado",
            "url": "https://suinavelda.paginasamarelas.cv/#products"
          },
          {
            "@type": "ListItem",
            "position": 4,
            "name": "Medicamentos para o gado",
            "url": "https://suinavelda.paginasamarelas.cv/#products"
          },
          {
            "@type": "ListItem",
            "position": 5,
            "name": "Equipamento para gado",
            "url": "https://suinavelda.paginasamarelas.cv/#products"
          }
        ]
      }
    },
    {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://suinavelda.paginasamarelas.cv/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Sobre nós",
          "item": "https://suinavelda.paginasamarelas.cv/#about"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "Serviços",
          "item": "https://suinavelda.paginasamarelas.cv/#services"
        },
        {
          "@type": "ListItem",
          "position": 4,
          "name": "Produtos",
          "item": "https://suinavelda.paginasamarelas.cv/#products"
        },
        {
          "@type": "ListItem",
          "position": 5,
          "name": "Contactos",
          "item": "https://suinavelda.paginasamarelas.cv/#contact"
        }
      ]
    }
  ]
}

  
</script>



</head>

<body>
 
     <!-- Main Navigation -->
    <nav id="main-nav" class="content-grid">
        <div class="nav-inner">
            <div class="logo">
                <img src="./img/logo.webp" srcset="./img/logo.webp 1x, ./img/logo.webp 2x" alt="Logo" title="Logo" width="73" height="70">
            </div>
            <div class="desktop-menu">
                <a href="/">Home</a>
                <a href="#about">Sobre nós</a>
                <a href="#services">Serviços</a>
                <a href="#products">Produtos</a>
                <a href="#contact">Contactos</a>
            
            </div>
            <button id="mobile-menu-toggle" class="mobile-menu-toggle">&#9776;</button>
            <a href="mailto:<EMAIL>?subject=Pedido%20de%20Orçamento%20solicitado%20através%20das%20Páginas%20Amarelas%20de%20Cabo%20Verde" class="contact-btn">Entre em contacto</a>
        </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu" class="mobile-menu">
        <div class="mobile-menu-content">
            <div class="menu-header">
                <img src="./img/logo.webp" srcset="./img/logo.webp 1x, ./img/logo.webp 2x" alt="Logo" title="Logo" width="73" height="70">
                <button id="close-mobile-menu">&times;</button>
            </div>
            <div class="menu-links">
                <a href="/">Home</a>
                 <a href="#about">Sobre nós</a>
                <a href="#services">Serviços</a>
                <a href="#products">Produtos</a>
                <a href="#contact">Contactos</a>
                <a href="mailto:<EMAIL>?subject=Pedido%20de%20Orçamento%20solicitado%20através%20das%20Páginas%20Amarelas%20de%20Cabo%20Verde" class="contact-btn">Entre em contacto</a>

              
            </div>
        </div>
    </div>



<section class="hero-slider">

  <!-- Slide 1 -->
  <div class="slide active">
    <div class="slide-bg">
      <picture>
        <source 
          media="(max-width: 799px)" 
          type="image/webp" 
          srcset="./img/slider_1a_scale,w_601.webp">
        <source 
          media="(min-width: 800px) and (max-width: 1214px)" 
          type="image/webp" 
          srcset="./img/slider_1a_scale,w_905.webp">
        <source 
          media="(min-width: 1215px)" 
          type="image/webp" 
          srcset="./img/slider_1a_scale,w_1351.webp">
        <img
          src="./img/slider_1a_scale,w_1351.webp"
          alt="slider 8"
          class="hero-img"
          width="1400"
          height="600"
          decoding="async"
          >
      </picture>
    </div>
    <div class="slide-overlay"></div>
    <div class="text-overlay"></div>
    <div class="content-grid">
      <div class="hero-content">
        <span class="h6">Suinave Lda</span>
        <h1>Cultivando Sucesso nos Campos e Indústrias</h1>
        <p>Fornecemos rações, máquinas, alimentos e serviços para aumentar a produtividade e tornar seu negócio mais sustentável.</p>
      <a  href="#services" class="explore-btn">Nossos serviços</a>
      </div>
    </div>
  </div>

 
</section>


    <div class="about-us" id="about">
        <div class="content-grid">
            <div class="about-content">
               <div class="about-image">
  <div class="image-container main-image">
<picture>
  <source 
    media="(max-width: 499px)" 
    type="image/webp" 
    srcset="./img/abt_image_scale,w_472.webp"
  >
  <source 
    media="(min-width: 500px)" 
    type="image/webp" 
    srcset="./img/abt_image_scale,w_635.webp"
  >
  <img
    src="./img/abt_image_scale,w_635.webp"
    alt="Suinave Lda about image"
    loading="lazy"
    title="About Suinave Lda"

  >
</picture>




  </div>

  <!-- <div class="image-container bottom-right-image">
    <picture>
      <source
        srcset="
          ./img/about_image-sm_scale,w_200.webp 200w,
          ./img/about_sm.webp 265w
        "
        sizes="(max-width: 265px) 100vw, 265px"
      >
      <img src="./img/about_sm.webp"
        alt="About image"
        loading="lazy"
        title="About image"

      >
    </picture>
  </div> -->
</div>

                <div class="about-text">
                    <span class="abt">Sobre a Suinave</span>
                    
                  <p>A Suinave é uma empresa consagrada no mercado nacional, com 23 anos de experiência na produção e comercialização de ração animal, bem como na transformação e empacotamento de gêneros alimentícios. Agora, a empresa chega ao mercado com uma nova iniciativa que promete revolucionar e rentabilizar a produção agrícola.</p>

          <p>Sob a liderança do sócio-gerente José Eduardo Tavares, a Suinave está investindo em maquinaria agrícola de ponta, como máquinas de embalamento de pasto, motocultivadores, enxadas motorizadas, tratores de alta e média potência, mini tratores, máquinas de debulhar milho e descascar feijão, charruas e moto-tanques para mobilização de água. Esse amplo portfólio de soluções tem o objetivo de combater o desperdício, suprir a escassez de mão-de-obra e aumentar a produtividade nos campos.
          </p>

          <p>Além disso, a Suinave mantém sua expertise na transformação e empacotamento de gêneros alimentícios, como milho, açúcar e sal, atendendo todo o mercado nacional. No setor animal, a empresa fornece uma vasta gama de ração, medicamentos e equipamentos para a pecuária.</p>
          <p>Com um investimento inicial estimado em 220 mil dólares, a Suinave está em fase de divulgação desta nova linha de negócios e busca parcerias com instituições financeiras para facilitar o acesso dos produtores rurais a essa maquinaria de ponta. A empresa reafirma seu compromisso em inovar, investir e atender às necessidades de seus clientes, sejam eles do ramo alimentício ou da agropecuária.</p>

               
                </div>
            </div>
        </div>
    </div>

<div class="features">
    <div class="pattern-overlay"></div>
    <div class="feature-container">
    <div class="content-grid">
      <div class="grid-container">
        
        <div class="cards-container">
        
          <div class="card" data-tilt="">
            <div class="card-header">
              <div class="card-icon">
            
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                </svg>
  
              </div>
              <span class="h3">Missão</span>
            </div>
            <p>Esforçamo-nos por melhorar a saúde e a produtividade do gado, fornecendo rações de primeira qualidade e
              maquinaria de ponta e oferecendo um apoio abrangente.
</p>
            <div class="card-overlay"></div>
          </div>
  
  
          <div class="card" data-tilt="">
            <div class="card-header">
              <div class="card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"></path>
                </svg>
              </div>
              <span class="h3">Visão</span>
            </div>
            <p>Revolucionar a gestão pecuária com tecnologia de ponta e produtos de qualidade superior.</p>
            <div class="card-overlay"></div>
          </div>
  
          <div class="card" data-tilt="">
            <div class="card-header">
              <div class="card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M6.429 9.75 2.25 12l4.179 2.25m0-4.5 5.571 3 5.571-3m-11.142 0L2.25 7.5 12 2.25l9.75 5.25-4.179 2.25m0 0L21.75 12l-4.179 2.25m0 0 4.179 2.25L12 21.75 2.25 16.5l4.179-2.25m11.142 0-5.571 3-5.571-3"></path>
                </svg>
              </div>
              <span class="h3">Valores fundamentais</span>
            </div>
            <ul class="custom-list">
              <li>
               
  
               <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                </svg>
  
                Qualidade
 </span>
              </li>
              <li>
               
               <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                </svg>
  
           Inovação
</span>
              </li>
              <li>
              
  
               <span><svg xmlns="http://www.w3.org/2000/svg" fill="none" width="34" height="34" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                </svg>  Integridade
 </span> 
              </li>
                
                


              <li>
              

               </li>
  
             
             
            </ul>
            <div class="card-overlay"></div>
          </div>
          
        </div>
      </div>
    </div>
    </div>
    
  </div>





   


        <div class="fireprod-header" id="services">
        <div class="fireprod-container">
            <h2 class="fireprod-section-title">Serviços
</h2>

            <div class="content-grid">
                <div class="fireprod-grid">
                    <div class="fireprod-intro">
                        <h2 class="fireprod-heading">Suinave Rações, Máquinas e Serviços em um Só Lugar </h2>
                        <p class="fireprod-description">Oferecemos rações de qualidade, máquinas modernas e soluções agrícolas completas — tudo pensado para aumentar a produtividade, a sustentabilidade e o sucesso no campo.</p>

                        <div class="fireprod-icons">
                            <a href="#" class="fireprod-icon" aria-label="Tractor">


                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 256 256"><path fill="currentColor" d="M244 164v-30a19.87 19.87 0 0 0-14.25-19.15l-.31-.09l-33.44-9.11V76a12 12 0 0 0-24 0v23.15l-16-4.33V60h4a12 12 0 0 0 0-24H40a12 12 0 0 0 0 24h4v24h-4a12 12 0 0 0 0 24h28a64.07 64.07 0 0 1 64 64v12a12 12 0 0 0 12 12h28.8a40 40 0 1 0 71.2-32m-112-52.33A87.75 87.75 0 0 0 68 84V60h64ZM175.35 172H156v-52.32L220 137v11.8a40.6 40.6 0 0 0-8-.8a40.06 40.06 0 0 0-36.65 24M212 204a16 16 0 1 1 16-16a16 16 0 0 1-16 16M64 124a52 52 0 1 0 52 52a52.06 52.06 0 0 0-52-52m0 80a28 28 0 1 1 28-28a28 28 0 0 1-28 28m16-28a16 16 0 1 1-16-16a16 16 0 0 1 16 16"/></svg>



                            </a>
                            <a href="#" class="fireprod-icon" aria-label="Service 2">
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 256 256"><g fill="currentColor"><path d="M223.85 216H24v-96a257 257 0 0 1 56.2 6.21l-.2-.07V80l64-48l64 48v41.11a255 255 0 0 0-40 7V96h-48v42.61A256.8 256.8 0 0 1 223.85 216" opacity="0.2"/><path d="M232 160a8 8 0 0 0 0-16a232.2 232.2 0 0 0-65.8 9.47q-9-5.78-18.46-10.78A246.3 246.3 0 0 1 232 128a8 8 0 0 0 0-16c-5.36 0-10.69.18-16 .49V80a8 8 0 0 0-3.2-6.4l-64-48a8 8 0 0 0-9.6 0l-64 48A8 8 0 0 0 72 80v36.37A266.3 266.3 0 0 0 24 112a8 8 0 0 0 0 16a247 247 0 0 1 193.61 93a8 8 0 1 0 12.48-10q-7.59-9.5-15.94-18.14c5.92-.57 11.89-.86 17.85-.86a8 8 0 0 0 0-16a201.5 201.5 0 0 0-32.59 2.65q-7.75-6.92-16-13.16A216.5 216.5 0 0 1 232 160m-104-26.76V104h32v17.94a262.5 262.5 0 0 0-31.93 11.33ZM88 84l56-42l56 42v29.93q-12.12 1.45-24 4V96a8 8 0 0 0-8-8h-48a8 8 0 0 0-8 8v31q-11.79-4.15-24-7.18Zm48.83 136.43a8 8 0 0 1-11.09 2.23A183.15 183.15 0 0 0 24 192a8 8 0 0 1 0-16a199.1 199.1 0 0 1 110.6 33.34a8 8 0 0 1 2.23 11.09m49.54-10.14a8 8 0 1 1-11.2 11.42A214.8 214.8 0 0 0 24 160a8 8 0 0 1 0-16a230.7 230.7 0 0 1 162.37 66.29"/></g></svg>
                            </a>
                            <a href="#" class="fireprod-icon" aria-label="Service 3">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M17.8 15.1a10 10 0 0 0 .9-7.1h.3c1.7 0 3-1.3 3-3V3h-3c-1.3 0-2.4.8-2.8 1.9a10 10 0 0 0-8.4 0C7.4 3.8 6.3 3 5 3H2v2c0 1.7 1.3 3 3 3h.3a10 10 0 0 0 .9 7.1M9 9.5v.5m6-.5v.5"/><path d="M15 22a4 4 0 1 0-3-6.6A4 4 0 1 0 9 22Zm-6-4h.01M15 18h.01"/></g></svg>
                            </a>
                        </div>
                    </div>

                    <div class="fireprod-card">
                        <img src="./img/Empacotamento.webp" alt="mpacotamento de géneros alimentícios (sal, milho, xerém)" title="mpacotamento de géneros alimentícios (sal, milho, xerém)" loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Empacotamento de géneros alimentícios (sal, milho, xerém)</p>
                            
                        </div>
                    </div>

                    <div class="fireprod-card">
                        <img src="./img/produto.webp" alt="Produção e comercialização de ração animal" title="Produção e comercialização de ração animal" loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Produção e comercialização de ração animal</p>
                          
                        </div>
                    </div>

           

       

                </div>
            </div>
        </div>
    </div>


  <div class="services-container" id="products">
    <div class="services-header">
      <h2 class="products-section-title">Produtos</h2>
      
    </div>
       <div class="content-grid">
    <div class="services-grid">

      <div class="service-item">

        <div class="service-image">
          <img src="./img/Alimentos_para_animais.webp" alt="Alimentos para animais"
            title="Alimentos para animais" loading="lazy" width="417" height="313">

        </div>
        <div class="service-content">
          <h3>Alimentos para animais

</h3>

<p>Os nossos produtos para alimentação animal são formulados para apoiar a saúde, o crescimento e a
              produtividade dos animais, assegurando que recebem nutrientes equilibrados e essenciais.



        </div>
      </div>

      <div class="service-item">

        <div class="service-image">
          <img src="./img/Maquinaria_industrial.webp" alt="Maquinaria industrial"
            title="Maquinaria industrial" loading="lazy" width="417" height="313">

        </div>
        <div class="service-content">
          <h3>Maquinaria industrial
</h3>

<p>As nossas máquinas são construídas com a mais recente tecnologia para garantir durabilidade, fiabilidade
              e desempenho superior, ajudando-o a simplificar os seus processos e a obter resultados óptimos.

</p>

        </div>
      </div>
      <div class="service-item">

        <div class="service-image">
          <img src="./img/Alimentos_para_o_gado.webp" alt="Alimentos para o gado"
            title="Alimentos para o gado" loading="lazy" width="417" height="313">
        </div>
        <div class="service-content">
          <h3>Alimentos para o gado
</h3>

          

<p>Para além de rações, fornecemos uma variedade de produtos alimentares adaptados ao consumo do gado.

</p>






        </div>
      </div>

        <div class="service-item">

        <div class="service-image">
          <img src="./img/Medicamentos.webp" alt="Medicamentos para o gado"
            title="Medicamentos para o gado" loading="lazy" width="417" height="313">
        </div>
        <div class="service-content">
          <h3>Medicamentos para o gado
</h3>

          

<p>Todos os nossos produtos são provenientes de fabricantes conceituados e são testados quanto à sua eficácia e segurança.

</p>






        </div>
      </div>

        <div class="service-item">

        <div class="service-image">
          <img src="./img/Equipamento_para_gado.webp" alt="Equipamento para gado"
            title="Equipamento para gado" loading="lazy" width="417" height="313">
        </div>
        <div class="service-content">
          <h3>Equipamento para gado
</h3>

          

<p>Concentramo-nos em fornecer produtos que melhoram o bem-estar dos animais e tornam as tarefas de gestão mais fáceis e mais eficientes para os agricultores.

</p>






        </div>
      </div>

      
     
      
      

    </div>
  </div>
  </div>

  <section class="contact" id="contact">
    <div class="content-grid">
      <h2 class="section-title">Contacte-nos</h2>
      <div class="contact-content">
        <div class="contact-map">


          <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3855.348572323124!2d-23.493944199999998!3d14.917660999999999!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x935991b0820a17b%3A0xcdb238f1d4dedb11!2sSuinave%20Lda!5e0!3m2!1sen!2ske!4v1758189794792!5m2!1sen!2ske" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade" title="Suinave Lda  Map"></iframe>


        </div>
        <div class="contact-info">
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0C158.75 0 80 78.75 80 176C80 267.4 256 512 256 512C256 512 432 267.4 432 176C432 78.75 353.25 0 256 0zM256 240C220.65 240 192 211.35 192 176C192 140.65 220.65 112 256 112C291.35 112 320 140.65 320 176C320 211.35 291.35 240 256 240z">
                </path>
              </svg>
            </span>
            <div>
              <h3>Endereço físico</h3>
              <p>Achada Grande Frente

            </div>
          </div>
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="m497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6">
                </path>
              </svg>
            </span>
            <div>
              <h3>Número de telefone</h3>
              <p><a href="tel:9914261">9914261
</a></p>
            
            </div>
          </div>
          <div class="contact-item">
            <span> <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7c22.4 17.4 52.1 39.5 154.1 113.6c21.1 15.4 56.7 47.8 92.2 47.6c35.7.3 72-32.8 92.3-47.6c102-74.1 131.6-96.3 154-113.7M256 320c23.2.4 56.6-29.2 73.4-41.4c132.7-96.3 142.8-104.7 173.4-128.7c5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9c30.6 23.9 40.7 32.4 173.4 128.7c16.8 12.2 50.2 41.8 73.4 41.4">
                </path>
              </svg></span>
            <div>
              <h3>Correio eletrónico</h3>
              <p><a
                  href="mailto:<EMAIL>?subject=Pedido%20de%20Orçamento%20solicitado%20através%20das%20Páginas%20Amarelas%20de%20Cabo%20Verde"><EMAIL></a>
              </p>
            </div>
          </div>
          <div class="contact-item">
            <span><svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0a256 256 0 1 1 0 512a256 256 0 1 1 0-512m-24 120v136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24">
                </path>
              </svg></span>
            <div>
              <h3>Horário de Funcionamento</h3>
              <p>Segunda a sexta-feira: 8:00 - 17:00</p>

<p>Sábado: 8:00- 13:00</p>


            </div>
          </div>
          <!-- Social Media Icons -->

          <div class="contact-item">


            <span><svg class="contact-icon" xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem"
                viewBox="0 0 16 16">
                <path fill="currentColor"
                  d="M12 10c-.8 0-1.4.3-2 .8L6.8 9c.1-.3.2-.7.2-1s-.1-.7-.2-1L10 5.2c.6.5 1.2.8 2 .8c1.7 0 3-1.3 3-3s-1.3-3-3-3s-3 1.3-3 3v.5L5.5 5.4C5.1 5.2 4.6 5 4 5C2.4 5 1 6.3 1 8c0 1.6 1.4 3 3 3c.6 0 1.1-.2 1.5-.4L9 12.5v.5c0 1.7 1.3 3 3 3s3-1.3 3-3s-1.3-3-3-3" />
              </svg></span>

            <div>
              <h3>Ligue-se a Nós</h3>


              <div class="social-links">
              
             
                  <!-- Whatsapp -->
               <a href="https://api.whatsapp.com/send/?phone=9914261&amp;text&amp;type=phone_number&amp;app_absent=0" target="_blank" class="social-link" aria-label="Whatsapp">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" color="currentColor">
                      <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12c0 1.379.28 2.693.784 3.888c.279.66.418.99.436 1.24c.017.25-.057.524-.204 1.073L2 22l3.799-1.016c.549-.147.823-.22 1.073-.204c.25.018.58.157 1.24.436A10 10 0 0 0 12 22"></path>
                      <path d="m8.588 12.377l.871-1.081c.367-.456.82-.88.857-1.488c.008-.153-.1-.841-.315-2.218C9.916 7.049 9.41 7 8.973 7c-.57 0-.855 0-1.138.13c-.358.163-.725.622-.806 1.007c-.064.305-.016.515.079.935c.402 1.783 1.347 3.544 2.811 5.009c1.465 1.464 3.226 2.409 5.01 2.811c.42.095.629.143.934.079c.385-.08.844-.448 1.008-.806c.129-.283.129-.568.129-1.138c0-.438-.049-.943-.59-1.028c-1.377-.216-2.065-.323-2.218-.315c-.607.036-1.032.49-1.488.857l-1.081.87"></path>
                    </g>
                  </svg>
                </a>
              
             
                <a href="https://www.facebook.com/p/Suinave-Lda-100068013262536/" target="_blank" class="social-link" aria-label="Facebook">
                
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 16 16"><path fill="currentColor" fill-rule="evenodd" d="M8.5 13.478a5.5 5.5 0 1 0-1.5-.069V9.75H5.75a.75.75 0 0 1 0-1.5H7V7.24c0-.884.262-1.568.722-2.032S8.843 4.5 9.644 4.5c.273 0 .612.04.948.213a.75.75 0 0 1-.685 1.334A.6.6 0 0 0 9.644 6c-.493 0-.737.144-.857.265c-.12.12-.287.39-.287.975v1.01h1.25a.75.75 0 0 1 0 1.5H8.5zM8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14" clip-rule="evenodd"></path></svg>
                </a>


              
               
              </div>
            </div>
          </div>
          
        </div>
      </div>
    </div>
  </section>

  <footer class="footer">
    <div class="content-grid">
      <div class="footer-content">
        <div class="copyright">
          <p>&copy; <span id="current-year"></span> Suinave Lda. Todos os direitos reservados.</p>

        </div>
        <div class="designer">
          <a href="https://www.paginasamarelas.cv/en" target="_blank" rel="noopener noreferrer">
            <img src="./img/yp_logo.webp" loading="lazy" alt="Paginas Amarelas Cabo Verde" width="50" height="50"
              title="Paginas Amarelas Cabo Verde">
            <p>Desenvolvido por Paginas Amarelas Cabo Verde</p>
          </a>
        </div>
      </div>
    </div>
  </footer>

  <script src="./js/testimonial.js"></script>
  <!-- <script src="./js/main.js"></script> -->

  <!-- <script src="https://cdn.jsdelivr.net/npm/vanilla-tilt@latest/dist/vanilla-tilt.min.js"></script> -->



  <script>
    document.getElementById('current-year').textContent = new Date().getFullYear();
  </script>

  <!-- <script>
    // Initialize VanillaTilt for 3D card effect
    VanillaTilt.init(document.querySelectorAll(".card"), {
      max: 5,
      speed: 400,
      glare: true,
      "max-glare": 0.2,
    });

    // Intersection Observer for scroll animations
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = 1;
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, {
      threshold: 0.1
    });

    // Observe all cards
    document.querySelectorAll('.card').forEach(card => {
      card.style.opacity = 0;
      card.style.transform = 'translateY(20px)';
      observer.observe(card);
    });
  </script> -->

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const topBar = document.getElementById('top-bar');
      const mainNav = document.getElementById('main-nav');
      const mainContent = document.querySelector('body'); // Adjust this selector if needed
      const mobileMenu = document.getElementById('mobile-menu');
      const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
      const closeMobileMenu = document.getElementById('close-mobile-menu');
      const mobileMenuLinks = document.querySelectorAll('.mobile-menu .menu-links a');

      // Mobile Menu Logic
      mobileMenuToggle.addEventListener('click', () => {
        mobileMenu.classList.add('show');
      });

      closeMobileMenu.addEventListener('click', () => {
        mobileMenu.classList.remove('show');
      });

      // Add click event listeners to all mobile menu links
      mobileMenuLinks.forEach(link => {
        link.addEventListener('click', function (event) {
          mobileMenu.classList.remove('show');

          const href = this.getAttribute('href');
          if (href.startsWith('#') && href !== '#') {
            event.preventDefault();

            const targetElement = document.querySelector(href);

            if (targetElement) {
              setTimeout(() => {
                const yOffset = -80;
                const y = targetElement.getBoundingClientRect().top + window.pageYOffset + yOffset;

                window.scrollTo({
                  top: y,
                  behavior: 'smooth'
                });
              }, 300);
            }
          }
        });
      });

      // Debug function to log sticky state
      function logStickyState() {
        //console.log('Scroll position:', window.scrollY);
        //console.log('mainNav has sticky class:', mainNav.classList.contains('sticky'));
        //console.log('mainNav style:', mainNav.style.cssText);
        //console.log('computed position:', window.getComputedStyle(mainNav).position);
      }

      // Improved Sticky Header Logic
      function handleScroll() {
        const scrollTop = window.scrollY || document.documentElement.scrollTop;
        //console.log('Scrolling, position:', scrollTop);

        if (scrollTop > 50) {
          // Make sure we're applying direct styles
          mainNav.style.position = 'fixed';
          mainNav.style.top = '0';
          mainNav.style.left = '0';
          mainNav.style.width = '100%';
          mainNav.style.zIndex = '100';
          mainNav.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
          mainNav.classList.add('sticky');

          // Add padding to body to prevent content jump
          mainContent.style.paddingTop = mainNav.offsetHeight + 'px';

          // Hide the top bar
          if (topBar) {
            topBar.style.display = 'none';
          }

          logStickyState();
        } else {
          // Remove direct styles
          mainNav.style.position = '';
          mainNav.style.top = '';
          mainNav.style.left = '';
          mainNav.style.width = '';
          mainNav.style.zIndex = '';
          mainNav.style.boxShadow = '';
          mainNav.classList.remove('sticky');

          // Remove padding from body
          mainContent.style.paddingTop = '0';

          // Show the top bar on desktop
          if (topBar && window.innerWidth >= 1024) {
            topBar.style.display = 'block';
          }

          logStickyState();
        }
      }

      // Initial check on page load
      handleScroll();

      // Add scroll event listener
      window.addEventListener('scroll', handleScroll);

      // Handle window resize
      window.addEventListener('resize', () => {
        if (window.innerWidth < 1024 && topBar) {
          topBar.style.display = 'none';
        } else if (window.scrollY <= 50 && topBar) {
          topBar.style.display = 'block';
        }

        // Recalculate sticky state on resize
        handleScroll();
      });
    });
  </script>



  <script>
   
    function throttle(fn, limit) {
      let waiting = false;
      return function (...args) {
        if (!waiting) {
          fn.apply(this, args);
          waiting = true;
          setTimeout(() => waiting = false, limit);
        }
      };
    }

    function handleParallaxScroll() {
      const elements = document.querySelectorAll('[data-parallax]');
      const scrollY = window.scrollY;

      elements.forEach(el => {
        const container = el.closest('.parallax-container');
        const rect = container.getBoundingClientRect();
        const offsetTop = container.offsetTop;
        const height = container.offsetHeight;

        // Only calculate if it's in view
        if (scrollY + window.innerHeight > offsetTop && scrollY < offsetTop + height) {
          const speed = 0.5; // Adjust this to control intensity
          const yPos = (scrollY - offsetTop) * speed;
          el.style.transform = `translateY(${yPos}px)`;
        }
      });
    }

    document.addEventListener('DOMContentLoaded', function () {
      window.addEventListener('scroll', throttle(handleParallaxScroll, 16)); // 60fps-ish
    });
  </script>
  
<script>
document.addEventListener("DOMContentLoaded", function () {
  const lazySections = document.querySelectorAll('.lazy-background');

  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const el = entry.target;
        const bgUrl = el.dataset.bg;

        // Create a new image to preload the background
        const img = new Image();
        img.src = bgUrl;

        img.onload = function () {
          // Only set background when fully loaded
          el.style.backgroundImage = `url('${bgUrl}')`;
          el.classList.add('loaded');
        };

        // Stop observing this element
        observer.unobserve(el);
      }
    });
  }, {
    rootMargin: '200px', // Preload a bit before the element enters the viewport
    threshold: 0.1
  });

  lazySections.forEach(section => observer.observe(section));
});
</script>

 


</body>

</html>