document.addEventListener("DOMContentLoaded",function(){let e=document.querySelector(".testimonials-container"),t=document.querySelectorAll(".testimonial"),n=document.querySelectorAll(".dot"),o=0,r,i=!1;function l(n=!0){let r=function e(){let n=window.getComputedStyle(t[0]);return parseInt(n.marginRight),t[0].offsetWidth+30}();n?e.style.transition="transform 0.5s ease-in-out":e.style.transition="none",e.style.transform=`translateX(-${r*(o+t.length)}px)`,n||e.offsetHeight}function s(){i||(i=!0,o++,l(),a(),setTimeout(()=>{o>=t.length&&(o%=t.length,l(!1)),i=!1},500))}function c(){i||(i=!0,o--,l(),a(),setTimeout(()=>{o<0&&(o=(o%t.length+t.length)%t.length,l(!1)),i=!1},500))}function a(){n.forEach((e,n)=>{e.classList.remove("active"),n===Math.abs(o%t.length)&&e.classList.add("active")})}function d(){clearInterval(r),r=setInterval(s,3e3)}var $,f=250;let h;window.addEventListener("resize",($=function(){l(!1)},function e(...t){let n=()=>{clearTimeout(h),$(...t)};clearTimeout(h),h=setTimeout(n,250)})),n.forEach((e,t)=>{e.addEventListener("click",()=>{i||(clearInterval(r),o=t,l(),a(),d())})}),document.addEventListener("keydown",e=>{"ArrowRight"===e.key?(clearInterval(r),s(),d()):"ArrowLeft"===e.key&&(clearInterval(r),c(),d())}),function n(){if(t.length<2)return;let o=Array.from(t);o.forEach(t=>{let n=t.cloneNode(!0);e.appendChild(n)});for(let i=t.length-1;i>=0;i--){let a=t[i].cloneNode(!0);e.insertBefore(a,e.firstChild)}l(!1),d();let $=0,f=0;e.addEventListener("touchstart",e=>{$=e.changedTouches[0].screenX,clearInterval(r)}),e.addEventListener("touchend",e=>{(f=e.changedTouches[0].screenX)<$-50?s():f>$+50&&c(),d()})}()});