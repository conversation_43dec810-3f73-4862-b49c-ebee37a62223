<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>Eco Brain Contractors: Reimagining the Future of Building</title>
  <meta name="description"
    content="Revolutionizing construction with eco-friendly EPS panels & prefab solutions. Build smarter—cold rooms, flat-packs & more. Contact us today!">


  <meta name="keywords" content="Eco Brain Contractors">

  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <!-- Favicon -->
  <link rel="icon" href="./favicon/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="./favicon/apple-touch-icon.jpg">
  <link rel="icon" type="image/jpg" sizes="32x32" href="./favicon/favicon-32x32.jpg">
  <link rel="icon" type="image/jpg" sizes="16x16" href="./favicon/favicon-16x16.jpg">
  <link rel="manifest" href="./favicon/site.webmanifest.json">
  <!-- Robots -->
  <meta name="robots" content="index, follow">
  <!-- Site Published Date -->
  <meta property="article:published_time" content="2025-07-16">
  <!-- Google Verification -->
  <!-- <meta name="google-site-verification" content="Your Google Search Console Verification Code"> -->
   <meta name="google-site-verification" content="N0HompzJ25uz2PejDBXT-26yePXEWujgsnsWOcVrNQQ" />
   
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://ecobraincontractorslimited.yellowpageskenya.com">
  <meta property="og:title" content="Eco Brain Contractors: Reimagining the Future of Building">
  <meta property="og:description"
    content="Revolutionizing construction with eco-friendly EPS panels & prefab solutions. Build smarter—cold rooms, flat-packs & more. Contact us today!">
  <meta property="og:image" content="https://ecobraincontractorslimited.yellowpageskenya.com/img/logo.webp">
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:site" content="@yellowpages254">
  <meta property="twitter:url" content="https://ecobraincontractorslimited.yellowpageskenya.com ">
  <meta property="twitter:title" content="Eco Brain Contractors: Reimagining the Future of Building ">
  <meta property="twitter:description"
    content="Revolutionizing construction with eco-friendly EPS panels & prefab solutions. Build smarter—cold rooms, flat-packs & more. Contact us today!">
  <meta property="twitter:image" content="https://ecobraincontractorslimited.yellowpageskenya.com/img/logo.webp">
  <!-- Canonical URL -->
  <link rel="canonical" href="https://ecobraincontractorslimited.yellowpageskenya.com">
  <!-- Hreflang tags -->
  <link rel="alternate" hreflang="en" href="https://ecobraincontractorslimited.yellowpageskenya.com">

  <!-- <link rel="alternate" hreflang="en" href="https://ecobraincontractorslimited.yellowpageskenya.com/"> -->
  <link rel="alternate" hreflang="x-default" href="https://ecobraincontractorslimited.yellowpageskenya.com/">
  <!-- Include more hreflang tags here if you have the website available in other languages -->
  <!-- Sitemap -->
  <link rel="sitemap" type="application/xml" title="Sitemap"
    href="https://ecobraincontractorslimited.yellowpageskenya.com/sitemap.xml">

  <!-- Preconnect to Google Maps APIs -->
  <link rel="preconnect" href="https://maps.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://maps.gstatic.com" crossorigin>

  <link rel="preload" as="image" href="./img/slider_8.webp" fetchpriority="high">

  <!-- Internal CSS -->

  <link rel="stylesheet" href="css/features.css">
  <link rel="stylesheet" href="css/ots.css">
  <link rel="stylesheet" href="css/s2.css">
  <link rel="stylesheet" href="css/hero.css">
  <link rel="stylesheet" href="css/service-section.css">
  <link rel="stylesheet" href="css/mn.css">
  <link rel="stylesheet" href="css/about.css">
  <link rel="stylesheet" href="css/main.css">
  <link rel="stylesheet" href="css/services.css">
  <link rel="stylesheet" href="css/testimonial.css">
   
  
  
 
  <style>
    html {
      scroll-behavior: smooth;
    }

    /* Inline critical font styles for Poppins */
    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 400;
      src: local('Poppins Regular'), local('Poppins-Regular'), url(https://fonts.gstatic.com/s/poppins/v15/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 700;
      src: local('Poppins Bold'), local('Poppins-Bold'), url(https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.woff2) format('woff2');
      font-display: swap;
    }

    /* Inline critical font styles for Work Sans */

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 400;
      src: local('Work Sans Regular'),
        local('WorkSans-Regular'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 600;
      src: local('Work Sans SemiBold'),
        local('WorkSans-SemiBold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 700;
      src: local('Work Sans Bold'),
        local('WorkSans-Bold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }


    /* body {
      font-family: 'Work Sans', sans-serif;
    } */

    .mobile-menu {
      transition: transform 0.3s ease-in-out;
    }

    .mobile-menu.hidden {
      transform: translateX(-100%);
    }

    #top-bar {
      transition: transform 0.3s ease-out, opacity 0.3s ease-out;
    }

    #main-nav {
      transition: all 0.3s ease-out;
    }

    .sticky {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 50;
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  </style>


<script type="application/ld+json">

  {
  "website": {
    "title": "Eco Brain Contractors: Reimagining the Future of Building",
    "description": "Revolutionizing construction with eco-friendly EPS panels & prefab solutions. Build smarter—cold rooms, flat-packs & more. Contact us today!",
    "language": "en",
    "url": "https://ecobraincontractorslimited.yellowpageskenya.com",
    "favicon": {
      "ico": "./favicon/favicon.ico",
      "apple_touch_icon": "./favicon/apple-touch-icon.jpg",
      "icons": [
        {"size": "32x32", "path": "./favicon/favicon-32x32.jpg"},
        {"size": "16x16", "path": "./favicon/favicon-16x16.jpg"}
      ],
      "manifest": "./favicon/site.webmanifest.json"
    },
    "meta": {
      "keywords": "Eco Brain Contractors",
      "viewport": "width=device-width, initial-scale=1.0",
      "robots": "index, follow",
      "published_time": "2025-07-16",
      "google_verification": "N0HompzJ25uz2PejDBXT-26yePXEWujgsnsWOcVrNQQ"
    },
    "analytics": {
      "google_tag": "G-62YRXTBVE8"
    },
    "navigation": {
      "logo": {
        "path": "./img/logo.webp",
        "srcset": "./img/logo.webp 1x, ./img/logo.webp 2x",
        "alt": "Logo",
        "title": "Logo",
        "width": 134,
        "height": 64
      },
      "links": [
        {"text": "Home", "url": "/"},
        {"text": "About Us", "url": "#about"},
        {"text": "Services", "url": "#services"},
        {"text": "Contact", "url": "#contact"}
      ],
      "contact_button": {
        "text": "Get In Touch",
        "url": "mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya"
      },
      "contact_info": [
        {
          "type": "email",
          "value": "<EMAIL>",
          "subject": "Quote Requested through Yellow Pages Kenya"
        },
        {
          "type": "phone",
          "value": "0710842717"
        },
        {
          "type": "hours",
          "value": "Mon - Fri: 8:00am – 5:00pm"
        }
      ],
      "social_media": [
        {
          "platform": "whatsapp",
          "url": "https://api.whatsapp.com/send/?phone=0710842717&text&type=phone_number&app_absent=0"
        },
        {
          "platform": "facebook",
          "url": "https://www.facebook.com/PrefabMaterialsKenya"
        }
      ]
    },
    "sections": [
      {
        "type": "hero_slider",
        "slides": [
          {
            "title": "Advanced Insulated Panels for Smarter Builds",
            "subtitle": "Manufacturing EPS Sandwich wall and roof panels",
            "description": "Revolutionize your construction projects with our high-performance EPS Sandwich Wall and Roof Panels—engineered for superior insulation, strength, and energy efficiency.",
            "button": {
              "text": "View More",
              "url": "#services"
            },
            "image": {
              "path": "./img/slider_8_1400.webp",
              "sources": [
                {"media": "(max-width: 799px)", "srcset": "./img/slider_8_976.webp"},
                {"media": "(min-width: 800px) and (max-width: 1214px)", "srcset": "./img/slider_8_1249.webp"},
                {"media": "(min-width: 1215px)", "srcset": "./img/slider_8_1400.webp"}
              ],
              "alt": "Prefab Containers – Precision Fabrication, Sustainable Construction",
              "width": 1400,
              "height": 600
            }
          },
          {
            "title": "Innovative 3D Panels for Smarter Construction",
            "subtitle": "Manufacturing EPS 3D Panels",
            "description": "We manufacture high-quality EPS 3D panels that offer excellent structural strength, thermal insulation, and design flexibility—ideal for fast, efficient, and sustainable construction projects.",
            "button": {
              "text": "Explore",
              "url": "#services"
            },
            "image": {
              "path": "./img/slider_1_1400.webp",
              "sources": [
                {"media": "(max-width: 799px)", "srcset": "./img/slider_1_986.webp"},
                {"media": "(min-width: 800px) and (max-width: 1214px)", "srcset": "./img/slider_1_1241.webp"},
                {"media": "(min-width: 1215px)", "srcset": "./img/slider_1_1400.webp"}
              ],
              "alt": "Manufacturing EPS 3D Panels",
              "width": 1400,
              "height": 600
            }
          },
          {
            "title": "Your Trusted Prefab Structures Specialist",
            "subtitle": "Prefab Structures Construction",
            "description": "As prefab specialists, we deliver high-quality, cost-effective builds that reduce timelines, cut waste, and meet modern construction demands with precision and care.",
            "button": {
              "text": "Contact Us",
              "url": "mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Tanzania"
            },
            "image": {
              "path": "./img/slider_3_1400.webp",
              "sources": [
                {"media": "(max-width: 799px)", "srcset": "./img/slider_3_944.webp"},
                {"media": "(min-width: 800px) and (max-width: 1214px)", "srcset": "./img/slider_3_1190.webp"},
                {"media": "(min-width: 1215px)", "srcset": "./img/slider_3_1400.webp"}
              ],
              "alt": "Prefab Structures Construction",
              "width": 1400,
              "height": 600
            }
          }
        ]
      },
      {
        "type": "features",
        "title": "Core Values",
        "description": "At Eco Brain Contractors, our core values shape every project we undertake. Commitment, innovation, and excellence drive us to deliver sustainable, high-quality construction solutions that stand the test of time.",
        "cards": [
          {
            "title": "Mission",
            "icon": "check-circle",
            "description": "We aim to revolutionize the construction industry by offering cutting-edge EPS-insulated panels and services that promote sustainability, energy efficiency, and responsible building practices."
          },
          {
            "title": "Vision",
            "icon": "calendar",
            "description": "We strive to push the boundaries of innovation, creating structures that inspire and endure."
          },
          {
            "title": "Core Values",
            "icon": "layers",
            "values": [
              "Commitment",
              "Innovation",
              "Excellence"
            ]
          }
        ]
      },
      {
        "type": "about",
        "title": "About Us",
        "description": [
          "Discover the future of construction with E.B.C. As pioneers in the industry, we're dedicated to revolutionizing how buildings are made, blending innovation with sustainability at every step. From cutting-edge EPS insulated panels to expert construction services, we're here to shape a greener, more efficient world, one structure at a time. Explore our range of solutions and join us in building a brighter tomorrow.",
          "At E.B.C, we pride ourselves on being the forefront manufacturers and Contractors of EPS Insulated Sandwich Panels and EPS 3D panels. With a commitment to sustainability and innovation, we harness the power of modular insulated panels to revolutionize the way we build.",
          "From constructing Cold rooms and Commercial Structures to providing Flat-Pack solutions and insulating Shipping Containers, we offer versatile and eco-friendly building materials and services tailored to meet your needs.",
          "Also, as trusted Building Material suppliers and contractors, we specialize in Prefab structures, making construction projects efficient and cost-effective. Our team of experts is dedicated to delivering high-quality solutions that exceed expectations, ensuring every project is completed with precision and excellence. Reach us today!"
        ],
        "image": {
          "path": "./img/about_image_635.webp",
          "sources": [
            {"media": "(max-width: 499px)", "srcset": "./img/about_image_482.webp"}
          ],
          "alt": "ARS about image"
        },
        "button": {
          "text": "View Profile",
          "url": "./img/Eco_Brains_Company_Profile.pdf",
          "target": "_blank"
        }
      },
      {
        "type": "industries",
        "title": "Industries We Serve",
        "industries": [
          {
            "name": "Banking",
            "icon": "bank"
          },
          {
            "name": "Capital Markets",
            "icon": "chart-line"
          },
          {
            "name": "Manufacturing",
            "icon": "factory"
          },
          {
            "name": "Healthcare",
            "icon": "home-heart"
          },
          {
            "name": "Higher Education",
            "icon": "school"
          }
        ]
      },
      {
        "type": "services",
        "title": "Our Services",
        "services": [
          {
            "title": "Manufacturing EPS Insulated Sandwich Panels",
            "description": "We specialize in producing high-quality EPS Insulated Sandwich Panels, providing superior insulation properties for various applications.",
            "image": {
              "path": "./img/EPS_Insulated_Sandwich.webp",
              "alt": "EPS Insulated Sandwich Panels",
              "width": 417,
              "height": 313
            }
          },
          {
            "title": "Manufacturing EPS 3D Panels",
            "description": "Our EPS 3D panels offer innovative solutions for construction, with intricate designs and excellent structural integrity.",
            "image": {
              "path": "./img/EPS_3D_Panels.webp",
              "alt": "EPS 3D Panels",
              "width": 417,
              "height": 313
            }
          },
          {
            "title": "Building Cold Rooms",
            "description": "We design and construct cold rooms using modular insulated panels, ensuring optimal temperature control for a variety of industries such as food storage and pharmaceuticals.",
            "image": {
              "path": "./img/Cold_Room.webp",
              "alt": "Cold Room Construction",
              "width": 417,
              "height": 313
            }
          },
          {
            "title": "Constructing Commercial Structures",
            "description": "From offices to warehouses, our team is experienced in building commercial structures using our eco-friendly panels, delivering durable and energy-efficient solutions.",
            "image": {
              "path": "./img/Commercial_Structures2.webp",
              "alt": "Commercial Structures Construction",
              "width": 417,
              "height": 313
            }
          },
          {
            "title": "Providing Flat-Pack Solutions",
            "description": "We offer flat-pack solutions for easy transportation and assembly, ideal for temporary structures, emergency shelters, or remote locations.",
            "image": {
              "path": "./img/Flat_Pack_Solutions.webp",
              "alt": "Flat-Pack Solutions",
              "width": 417,
              "height": 313
            }
          },
          {
            "title": "Insulating Shipping Containers",
            "description": "Transform standard shipping containers into insulated spaces suitable for various purposes, including offices, living spaces, or storage units.",
            "image": {
              "path": "./img/Shipping_Containers.webp",
              "alt": "Shipping Container Insulation",
              "width": 417,
              "height": 313
            }
          },
          {
            "title": "Supply Building Materials",
            "description": "As trusted suppliers, we offer a wide range of building materials, including insulated panels and accessories, to support construction projects of all sizes.",
            "image": {
              "path": "./img/Prefab_Structures2.webp",
              "alt": "Building Materials Supply",
              "width": 417,
              "height": 313
            }
          },
          {
            "title": "Prefab Structures Specialist",
            "description": "Our expertise in prefab structures ensures efficient and cost-effective construction methods, reducing project timelines and minimizing environmental impact.",
            "image": {
              "path": "./img/Prefab_Structures.webp",
              "alt": "Prefab Structures",
              "width": 417,
              "height": 313
            }
          },
          {
            "title": "Construction Services",
            "description": "Whether it's new construction, renovations, or additions, our team of experienced contractors provides reliable construction services tailored to your specific requirements.",
            "image": {
              "path": "./img/Construction_Services.webp",
              "alt": "Construction Services",
              "width": 417,
              "height": 313
            }
          }
        ]
      },
      {
        "type": "contact",
        "title": "Contact Us",
        "map": {
          "src": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3988.892673079323!2d36.8662731!3d-1.234219!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x182f17340e8db643%3A0x7e29366d2048df1a!2sEco%20Brains%20Contractors!5e0!3m2!1sen!2ske!4v1752662972226!5m2!1sen!2ske",
          "title": "Eco Brain Contractors Map"
        },
        "contact_info": [
          {
            "type": "address",
            "icon": "location",
            "title": "Physical Location",
            "value": "House Seven Business Park Nairobi Garden Estate Garden Court Rd 2nd Avenue B32"
          },
          {
            "type": "phone",
            "icon": "phone",
            "title": "Phone number",
            "value": ["0710842717", "0780842717"]
          },
          {
            "type": "email",
            "icon": "email",
            "title": "Email",
            "value": "<EMAIL>",
            "subject": "Quote Requested through Yellow Pages Kenya"
          },
          {
            "type": "hours",
            "icon": "clock",
            "title": "Operating Hours",
            "value": "Monday to Friday: 8:00am – 5:00pm"
          }
        ],
        "social_media": [
          {
            "platform": "whatsapp",
            "url": "https://api.whatsapp.com/send/?phone=0710842717&text&type=phone_number&app_absent=0"
          },
          {
            "platform": "facebook",
            "url": "https://www.facebook.com/PrefabMaterialsKenya"
          }
        ]
      }
    ],
    "footer": {
      "copyright": "© [current-year] Eco Brain Contractors. All Rights Reserved.",
      "developer": {
        "name": "Powered by Yellow Pages Kenya.",
        "logo": "./img/yp_logo.webp",
        "alt": "Yellow Pages Kenya",
        "url": "https://www.yellowpageskenya.com/"
      }
    },
    "stylesheets": [
      "css/features.css",
      "css/ots.css",
      "css/s2.css",
      "css/hero.css",
      "css/service-section.css",
      "css/mn.css",
      "css/about.css",
      "css/main.css",
      "css/services.css",
      "css/testimonial.css"
    ],
    "scripts": [
      {
        "type": "google_analytics",
        "id": "G-62YRXTBVE8",
        "trigger": ["scroll", "mousemove", "touchstart"]
      },
      {
        "type": "external",
        "src": "./js/testimonial.js"
      },
      {
        "type": "external",
        "src": "https://cdn.jsdelivr.net/npm/vanilla-tilt@latest/dist/vanilla-tilt.min.js"
      },
      {
        "type": "inline",
        "content": "document.getElementById('current-year').textContent = new Date().getFullYear();"
      }
    ]
  }
}
</script>

</head>

<body>
  <div class="content-grid" data-theme="blue">
    <div id="top-bar">
      <div class="top-bar-inner">
        <div class="contact-info-topbar">
          <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
           <EMAIL>
          </a>
          <span class="separator"></span>
          <span class="working-hours">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
             Mon - Fri: 8:00am – 5:00pm 
          </span>
        </div>
        <div class="phone-and-social">
          <div class="phone-number phon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
              <path
                d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
              </path>
            </svg>
            <a href="tel:0710842717">0710842717</a>
          </div>
          <div class="social-icons">

           <!-- whatsapp -->
              <a href="https://api.whatsapp.com/send/?phone=0710842717&amp;text&amp;type=phone_number&amp;app_absent=0" target="_blank" class="social-icon" aria-label="whatsapp">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" color="currentColor">
                  <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12c0 1.379.28 2.693.784 3.888c.279.66.418.99.436 1.24c.017.25-.057.524-.204 1.073L2 22l3.799-1.016c.549-.147.823-.22 1.073-.204c.25.018.58.157 1.24.436A10 10 0 0 0 12 22"></path>
                  <path d="m8.588 12.377l.871-1.081c.367-.456.82-.88.857-1.488c.008-.153-.1-.841-.315-2.218C9.916 7.049 9.41 7 8.973 7c-.57 0-.855 0-1.138.13c-.358.163-.725.622-.806 1.007c-.064.305-.016.515.079.935c.402 1.783 1.347 3.544 2.811 5.009c1.465 1.464 3.226 2.409 5.01 2.811c.42.095.629.143.934.079c.385-.08.844-.448 1.008-.806c.129-.283.129-.568.129-1.138c0-.438-.049-.943-.59-1.028c-1.377-.216-2.065-.323-2.218-.315c-.607.036-1.032.49-1.488.857l-1.081.87"></path>
                </g>
              </svg>
            </a>

            <!-- Facebook-->
            <a href="https://www.facebook.com/PrefabMaterialsKenya" target="_blank" class="social-icon" aria-label="Facebook">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                <path fill="currentColor"
                  d="m16.117 8.906l-2.589 3.086l-3.715-2.281a.5.5 0 0 0-.644.104l-3.052 3.636a.5.5 0 0 0 .766.643l2.774-3.306l3.715 2.281c.211.13.485.085.645-.104l2.866-3.416a.5.5 0 0 0-.766-.643M12 1C5.715 1 .975 5.594.975 11.686a10.43 10.43 0 0 0 3.471 7.905c.071.06.114.149.118.242l.058 1.867a1.343 1.343 0 0 0 1.883 1.187l2.088-.92a.33.33 0 0 1 .226-.018c1.037.283 2.107.425 3.181.422c6.285 0 11.025-4.594 11.025-10.685S18.285 1 12 1m0 20.371a11 11 0 0 1-2.916-.387a1.36 1.36 0 0 0-.894.068l-2.086.919a.35.35 0 0 1-.324-.026a.34.34 0 0 1-.158-.276l-.058-1.871a1.34 1.34 0 0 0-.45-.952a9.45 9.45 0 0 1-3.14-7.16C1.975 6.164 6.285 2 12 2s10.025 4.164 10.025 9.686S17.715 21.37 12 21.37" />
              </svg>
            </a>
           
            
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Navigation -->
  <nav id="main-nav" class="content-grid dark-theme">
    <div class="nav-inner">
      <div class="logo">
        <!-- <img src="./img/logo.webp" alt="Logo" title="Logo" width="190" height="53"> -->

  <img
  src="./img/logo.webp"
  srcset="./img/logo.webp 1x, ./img/logo.webp 2x"
  alt="Logo"
  title="Logo"
  width="134"
  height="64"
>

      </div>
      <div class="desktop-menu">
        <a href="/">Home</a>
        <a href="#about">About Us</a>
        <a href="#services">Services</a>
        <a href="#contact">Contact</a>
      </div>
      <button id="mobile-menu-toggle" class="mobile-menu-toggle">&#9776;</button>
      <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya" class="contact-btn">Get
        In Touch</a>
    </div>
  </nav>

  <!-- Mobile Menu Overlay -->
  <div id="mobile-menu" class="mobile-menu">
    <div class="mobile-menu-content">
      <div class="menu-header">
        <!-- <img src="./img/logo.webp" alt="Logo" title="Logo"> -->


<img
  src="./img/logo.webp"
  srcset="./img/logo.webp 1x, ./img/logo.webp 2x"
  alt="Logo"
  title="Logo"
  width="134"
  height="64"
>

        <button id="close-mobile-menu">&times;</button>
      </div>
      <div class="menu-links">
        <a href="/">Home</a>
        <a href="#about">About Us</a>
        <a href="#services">Services</a>
        <a href="#contact">Contact</a>

        <!-- button mobile menu -->
        <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya"
          class="contact-btn">Get In Touch</a>
      </div>
    </div>
  </div>



<section class="hero-slider">
  <!-- Slide 1 -->
  <div class="slide active">
    <div class="slide-bg">
      <picture>
        <source 
          media="(max-width: 799px)" 
          type="image/webp" 
          srcset="./img/slider_8_976.webp">
        <source 
          media="(min-width: 800px) and (max-width: 1214px)" 
          type="image/webp" 
          srcset="./img/slider_8_1249.webp">
        <source 
          media="(min-width: 1215px)" 
          type="image/webp" 
          srcset="./img/slider_8_1400.webp">
        <img
          src="./img/slider_8_1400.webp"
          alt="Prefab Containers – Precision Fabrication, Sustainable Construction"
          class="hero-img"
          width="1400"
          height="600"
          decoding="async"
          fetchpriority="high">
      </picture>
    </div>
    <div class="slide-overlay"></div>
    <div class="text-overlay"></div>
    <div class="content-grid">
      <div class="hero-content">
        <span class="h6">Manufacturing EPS Sandwich wall and roof panels</span>
        <h1>Advanced Insulated Panels for Smarter Builds</h1>
        <p>Revolutionize your construction projects with our high-performance EPS Sandwich Wall and Roof Panels—engineered for superior insulation, strength, and energy efficiency.</p>
        <a href="#services" class="explore-btn">View More</a>
      </div>
    </div>
  </div>

  <!-- Slide 2 -->
  <div class="slide">
    <div class="slide-bg">
      <picture>
        <source 
          media="(max-width: 799px)" 
          type="image/webp" 
          srcset="./img/slider_1_986.webp">
        <source 
          media="(min-width: 800px) and (max-width: 1214px)" 
          type="image/webp" 
          srcset="./img/slider_1_1241.webp">
        <source 
          media="(min-width: 1215px)" 
          type="image/webp" 
          srcset="./img/slider_1_1400.webp">
        <img
          src="./img/slider_1_1400.webp"
          alt="Manufacturing EPS 3D Panels"
          class="hero-img"
          width="1400"
          height="600"
          decoding="async">
      </picture>
    </div>
    <div class="slide-overlay"></div>
    <div class="text-overlay"></div>
    <div class="content-grid">
      <div class="hero-content">
        <span class="h6">Manufacturing EPS 3D Panels </span>
        <div class="ht">Innovative 3D Panels for Smarter Construction</div>
        <p>We manufacture high-quality EPS 3D panels that offer excellent structural strength, thermal insulation, and design flexibility—ideal for fast, efficient, and sustainable construction projects.</p>
        <a href="#services" class="explore-btn">Explore</a>
      </div>
    </div>
  </div>

  <!-- Slide 3 -->
  <div class="slide">
    <div class="slide-bg">
      <picture>
        <source 
          media="(max-width: 799px)" 
          type="image/webp" 
          srcset="./img/slider_3_944.webp">
        <source 
          media="(min-width: 800px) and (max-width: 1214px)" 
          type="image/webp" 
          srcset="./img/slider_3_1190.webp">
        <source 
          media="(min-width: 1215px)" 
          type="image/webp" 
          srcset="./img/slider_3_1400.webp">
        <img
          src="./img/slider_3_1400.webp"
          alt="Prefab Structures Construction"
          class="hero-img"
          width="1400"
          height="600"
          decoding="async">
      </picture>
    </div>
    <div class="slide-overlay"></div>
    <div class="text-overlay"></div>
    <div class="content-grid">
      <div class="hero-content">
        <span class="h6">Prefab Structures Construction</span>
        <div class="ht">Your Trusted Prefab Structures Specialist</div>
        <p>As prefab specialists, we deliver high-quality, cost-effective builds that reduce timelines, cut waste, and meet modern construction demands with precision and care.</p>
        <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Tanzania" class="explore-btn">Contact Us</a>
      </div>
    </div>
  </div>

  <!-- Slider controls -->
  <div class="slider-controls">
    <div class="slider-dot active" data-index="0"></div>
    <div class="slider-dot" data-index="1"></div>
    <div class="slider-dot" data-index="2"></div>
  </div>

  <!-- Slider arrows -->
  <div class="slider-arrows">
    <div class="arrow prev">
      <svg viewBox="0 0 24 24">
        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"></path>
      </svg>
    </div>
    <div class="arrow next">
      <svg viewBox="0 0 24 24">
        <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path>
      </svg>
    </div>
  </div>
</section>




  

  <section class="features">
    <div class="pattern-overlay"></div>
    <div class="feature-container">
    <div class="content-grid">
      <div class="grid-container">
        <div class="text-content">
          <h2 class="animate-text">Core Values</h2>
          <p class="animate-text-delay">At Eco Brain Contractors, our core values shape every project we undertake. Commitment, innovation, and excellence drive us to deliver sustainable, high-quality construction solutions that stand the test of time.</p>
        </div>
        <div class="cards-container">
        
          <div class="card" data-tilt>
            <div class="card-header">
              <div class="card-icon">
            
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg>
  
              </div>
              <h3>Mission</h3>
            </div>
            <p>We aim to revolutionize the construction industry by offering cutting-edge EPS-insulated panels and services that promote sustainability, energy efficiency, and responsible building practices. 
</p>
            <div class="card-overlay"></div>
          </div>
  
  
          <div class="card" data-tilt>
            <div class="card-header">
              <div class="card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z" />
                </svg>
              </div>
              <h3>Vision</h3>
            </div>
            <p>We strive to push the boundaries of innovation, creating structures that inspire and endure. 
</p>
            <div class="card-overlay"></div>
          </div>
  
          <div class="card" data-tilt>
            <div class="card-header">
              <div class="card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M6.429 9.75 2.25 12l4.179 2.25m0-4.5 5.571 3 5.571-3m-11.142 0L2.25 7.5 12 2.25l9.75 5.25-4.179 2.25m0 0L21.75 12l-4.179 2.25m0 0 4.179 2.25L12 21.75 2.25 16.5l4.179-2.25m11.142 0-5.571 3-5.571-3" />
                </svg>
              </div>
              <h3>Core Values</h3>
            </div>
            <ul class="custom-list">
              <li>
               
  
               <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg>
  
                Commitment  
 </span>
              </li>
              <li>
               
               <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg>
  
                Innovation 
</span>
              </li>
              <li>
              
  
               <span><svg xmlns="http://www.w3.org/2000/svg" fill="none" width="34" height="34"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg> Excellence  
 </span> 
              </li>
                
                


              <li>
              

               </li>
  
             
             
            </ul>
            <div class="card-overlay"></div>
          </div>
          
        </div>
      </div>
    </div>
    </div>
    
  </section>




 <div class="about-us" id="about">
        <div class="content-grid">
            <div class="about-content">
               <div class="about-image">
  <div class="image-container main-image">
<picture>
  <source 
    media="(max-width: 499px)" 
    type="image/webp" 
    srcset="./img/about_image_482.webp" 
  >
  <source 
    media="(min-width: 500px)" 
    type="image/webp"
    srcset="./img/about_image_482.webp"
  >
  <img
    src="./img/about_image_635.webp"
    alt="ARS about image"
    loading="lazy"
    style="width: 100%; height: 100%;"
  >
</picture>

  </div>

  <!-- <div class="image-container bottom-right-image">
    <picture>
      <source
        srcset="
          ./img/about_image-sm_200.webp 200w,
          ./img/about_image-sm_265.webp 265w"
        sizes="(max-width: 265px) 100vw, 265px"
      >
      <img
        src="./img/about_image-sm_265.webp"
        alt="About image"
        loading="lazy"
        style="width: 100%; height: 100%;"
      >
    </picture>
  </div> -->
</div>

                <div class="about-text">
                    <span class="abt">About Us</span>

                    <p>Discover the future of construction with E.B.C. As pioneers in the industry, we're dedicated to revolutionizing how buildings are made, blending innovation with sustainability at every step. From cutting-edge EPS insulated panels to expert construction services, we're here to shape a greener, more efficient world, one structure at a time. Explore our range of solutions and join us in building a brighter tomorrow.</p>
                    
                    <p>
                       At E.B.C, we pride ourselves on being the forefront manufacturers and Contractors of EPS Insulated Sandwich Panels and EPS 3D panels. With a commitment to sustainability and innovation, we harness the power of modular insulated panels to revolutionize the way we build. 

                    </p>

                    <p>
                      From constructing Cold rooms and Commercial Structures to providing Flat-Pack solutions and insulating Shipping Containers, we offer versatile and eco-friendly building materials and services tailored to meet your needs. 
                    </p>

                    <p>
                       Also, as trusted Building Material suppliers and contractors, we specialize in Prefab structures, making construction projects efficient and cost-effective. Our team of experts is dedicated to delivering high-quality solutions that exceed expectations, ensuring every project is completed with precision and excellence. Reach us today! 
                    </p>

             

                    <a href="./img/Eco_Brains_Company_Profile.pdf" target="_blank"  class="cta-button">View Profile</a>
                </div>
            </div>
        </div>
    </div>


  <div class="servicez-container">
    
 <div class="services-header">
      <h2 class="products-section-title">Industries We Serve
</h2>
    </div>

      <svg width="0" height="0" style="position:absolute">
  <defs>
    <linearGradient id="gold-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0e2b5cc4" />
      <stop offset="100%" stop-color="#0e2b5cc4" />
    </linearGradient>
  </defs>
</svg>

<div class="service-container">
  <div class="service-grid">
  
    <div class="service-card">

   

       <svg class="service-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 512 512"><path fill="url(#gold-gradient)" d="m243.4 2.6l-224 96c-14 6-21.8 21-18.7 35.8S16.8 160 32 160v8c0 13.3 10.7 24 24 24h400c13.3 0 24-10.7 24-24v-8c15.2 0 28.3-10.7 31.3-25.6s-4.8-29.9-18.7-35.8l-224-96c-8-3.4-17.2-3.4-25.2 0M128 224H64v196.3c-.6.3-1.2.7-1.8 1.1l-48 32c-11.7 7.8-17 22.4-12.9 35.9S17.9 512 32 512h448c14.1 0 26.5-9.2 30.6-22.7s-1.1-28.1-12.9-35.9l-48-32c-.6-.4-1.2-.7-1.8-1.1L448 224h-64v192h-40V224h-64v192h-48V224h-64v192h-40zM256 64a32 32 0 1 1 0 64a32 32 0 1 1 0-64"/></svg>

      <h3 class="service-title">Banking 


</h3>
      
 
    </div>

  
    <div class="service-card">

  

  <svg class="service-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="url(#gold-gradient)" d="m12 .834l6.373 3.823l-1.03 1.715l-.342-.206V8H7V6.166l-.343.206l-1.03-1.715zm-1 3.164v2.004h2.004V3.998zM22 10H2v12h6v-7h8v7h6z"/><path fill="url(#gold-gradient)" d="M10 17v5h4v-5z"/></svg>

      <h3 class="service-title">Capital Markets 
</h3>
    </div>

    <div class="service-card">

   

      <svg class="service-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 48 48"><path fill="url(#gold-gradient)" fill-rule="evenodd" d="M24.365 4.62a1.5 1.5 0 0 0-.746-1.986c-3.168-1.438-6.973-1.57-11.136-.042c-3.53 1.295-6.51 1.11-8.864.042a1.5 1.5 0 1 0-1.24 2.732c3.169 1.438 6.974 1.57 11.137.042c3.53-1.295 6.51-1.11 8.864-.042a1.5 1.5 0 0 0 1.985-.746m2.988 3.772a579 579 0 0 0-.478 4.108h14.249a579 579 0 0 0-.478-4.108c-.226-1.88-1.65-3.502-3.67-3.724a27 27 0 0 0-2.977-.168c-1.115 0-2.14.076-2.976.168c-2.021.222-3.444 1.844-3.67 3.724m-1.431 13.106h16.155a655 655 0 0 0-.622-5.998H26.543a659 659 0 0 0-.621 5.998m-9.478-6.673c2.82-1.08 5.682.998 5.805 3.889c.067 1.58.138 3.567.187 5.784h19.566a4.5 4.5 0 0 1 4.498 4.5v6.5c0 2.588-.163 4.604-.363 6.105c-.37 2.772-2.714 4.503-5.281 4.61c-3.131.129-8.615.285-16.856.285s-13.725-.156-16.856-.286c-2.568-.106-4.911-1.837-5.281-4.609c-.2-1.5-.363-3.517-.363-6.105v-7.547c0-2.34.872-4.73 2.843-6.316c2.33-1.876 6.342-4.604 12.1-6.81m2.554 27.156a.995.995 0 0 1-1.006.99c-2.31-.024-4.31-.06-6.02-.102a.994.994 0 0 1-.97-.99Q10.998 41.457 11 41c0-2.401.033-4.05.071-5.157c.046-1.328.72-2.51 2.033-2.713c.504-.078 1.128-.13 1.896-.13c.767 0 1.392.052 1.896.13c1.312.203 1.986 1.385 2.032 2.713C18.967 36.95 19 38.6 19 41q0 .512-.002.98M27 31.5a1.5 1.5 0 1 0 0 3h10a1.5 1.5 0 0 0 0-3zM25.5 39a1.5 1.5 0 0 1 1.5-1.5h10a1.5 1.5 0 1 1 0 3H27a1.5 1.5 0 0 1-1.5-1.5M23.62 8.634a1.5 1.5 0 1 1-1.24 2.732c-2.355-1.069-5.334-1.253-8.864.042c-4.163 1.528-7.968 1.396-11.137-.042a1.5 1.5 0 1 1 1.24-2.732c2.355 1.069 5.334 1.253 8.864-.042c4.162-1.528 7.968-1.396 11.136.042" clip-rule="evenodd"/></svg>

      <h3 class="service-title">Manufacturing </h3>
    </div>
    <div class="service-card">

      <svg class="service-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="url(#gold-gradient)" d="M21 20h2v2H1v-2h2V3a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1zM11 8H9v2h2v2h2v-2h2V8h-2V6h-2zm3 12h2v-6H8v6h2v-4h4z"/></svg>

      <h3 class="service-title">Healthcare</h3>
    </div>
    <div class="service-card">

      <svg class="service-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="url(#gold-gradient)" d="M5 13.18v4L12 21l7-3.82v-4L12 17zM12 3L1 9l11 6l9-4.91V17h2V9z"/></svg>

      <h3 class="service-title">Higher Education  
 </h3>
    </div>
 


  </div>
</div>

</div>





    <div class="content-grid" id="services">
        <div class="services-header">
            <h2 class="products-section-title">Our Services</h2>
        </div>
        <div class="services-grid">
            <div class="service-item">
                <div class="service-image">
                    <img src="./img/EPS_Insulated_Sandwich.webp" alt="EPS Insulated Sandwich Panels" title="EPS Insulated Sandwich Panels" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>Manufacturing EPS Insulated Sandwich Panels
</h3>

<p> We specialize in producing high-quality EPS Insulated Sandwich Panels, providing superior insulation properties for various applications. </p>
                    
                   
                </div>
              
            </div>
              <div class="service-item">
                <div class="service-image">
                    <img src="./img/EPS_3D_Panels.webp" alt="EPS 3D Panels" title="EPS 3D Panels" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>Manufacturing EPS 3D Panels
</h3>
<p>Our EPS 3D panels offer innovative solutions for construction, with intricate designs and excellent structural integrity. </p>
                    
                    
                </div>
                
            </div>
            <div class="service-item">
                <div class="service-image">
                    <img src="./img/Cold_Room.webp" alt="Cold Room Construction" title="Cold Room Construction" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>Building Cold Rooms
</h3>

<p>We design and construct cold rooms using modular insulated panels, ensuring optimal temperature control for a variety of industries such as food storage and pharmaceuticals. </p>
                    
                  
                </div>
                
            </div>

          
         
            <div class="service-item">
                <div class="service-image">
                    <img src="./img/Commercial_Structures2.webp" alt="Commercial Structures Construction" title="Commercial Structures Construction" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>Constructing Commercial Structures
</h3>

<p>From offices to warehouses, our team is experienced in building commercial structures using our eco-friendly panels, delivering durable and energy-efficient solutions. </p>
                   
                   
                </div>
                
            </div>

               <div class="service-item">
                <div class="service-image">
                    <img src="./img/Flat_Pack_Solutions.webp" alt="Flat-Pack Solutions" title="Flat-Pack Solutions" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>Providing Flat-Pack Solutions</h3>
                    <p>We offer flat-pack solutions for easy transportation and assembly, ideal for temporary structures, emergency shelters, or remote locations.</p>
                    
                
                </div>
                
            </div>


            <div class="service-item">
                <div class="service-image">
                    <img src="./img/Shipping_Containers.webp" alt="Shipping Container Insulation" title="Shipping Container Insulation" loading="lazy" width="417" height="313">
                </div>
                
                <div class="service-content">
                    <h3>Insulating Shipping Containers</h3>
                    <p>Transform standard shipping containers into insulated spaces suitable for various purposes, including offices, living spaces, or storage units.</p>
                    
                </div>
               
            </div>

                <div class="service-item">
                <div class="service-image">
                    <img src="./img/Prefab_Structures2.webp" alt="Building Materials Supply" title="Building Materials Supply" loading="lazy" width="417" height="313">
                </div>
                
                <div class="service-content">
                    <h3>Supply Building Materials
</h3>
<p>As trusted suppliers, we offer a wide range of building materials, including insulated panels and accessories, to support construction projects of all sizes. </p>
                    
                </div>
               
            </div>

                <div class="service-item">
                <div class="service-image">
                    <img src="./img/Prefab_Structures.webp" alt="Prefab Structures" title="Prefab Structures" loading="lazy" width="417" height="313">
                </div>
                
                <div class="service-content">
                    <h3>Prefab Structures Specialist
</h3>

<p>Our expertise in prefab structures ensures efficient and cost-effective construction methods, reducing project timelines and minimizing environmental impact.</p>
                    
                </div>
               
            </div>

                <div class="service-item">
                <div class="service-image">
                    <img src="./img/Construction_Services.webp" alt="Construction Services" title="Construction Services" loading="lazy" width="417" height="313">
                </div>
                
                <div class="service-content">
                    <h3>Construction Services
</h3>

<p>Whether it's new construction, renovations, or additions, our team of experienced contractors provides reliable construction services tailored to your specific requirements. </p>
                    
                </div>
               
            </div>

              

        </div>
    </div>




  <section class="contact" id="contact">
    <div class="content-grid">
      <h2 class="section-title">Contact Us</h2>
      <div class="contact-content">
        <div class="contact-map">
  

          <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3988.892673079323!2d36.8662731!3d-1.234219!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x182f17340e8db643%3A0x7e29366d2048df1a!2sEco%20Brains%20Contractors!5e0!3m2!1sen!2ske!4v1752662972226!5m2!1sen!2ske" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade" title="Eco Brain Contractors Map"></iframe>


        </div>
        <div class="contact-info">
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0C158.75 0 80 78.75 80 176C80 267.4 256 512 256 512C256 512 432 267.4 432 176C432 78.75 353.25 0 256 0zM256 240C220.**************.35 192 176C192 140.65 220.65 112 256 112C291.**************.65 320 176C320 211.35 291.35 240 256 240z">
                </path>
              </svg>
            </span>
            <div>
              <h3>Physical Location</h3>
              <p>House Seven Business Park Nairobi Garden Estate Garden Court Rd 2nd Avenue B32
</p>
              
            </div>
          </div>
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="m497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6">
                </path>
              </svg>
            </span>
            <div>
              <h3>Phone number</h3>
              <p><a href="tel:0710842717">0710842717
</a></p>

<p><a href="tel:0780842717">0780842717 
</a></p>
            </div>
          </div>
          <div class="contact-item">
            <span> <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7c22.4 17.4 52.1 39.5 154.1 113.6c21.1 15.4 56.7 47.8 92.2 47.6c35.7.3 72-32.8 92.3-47.6c102-74.1 131.6-96.3 154-113.7M256 320c23.2.4 56.6-29.2 73.4-41.4c132.7-96.3 142.8-104.7 173.4-128.7c5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9c30.6 23.9 40.7 32.4 173.4 128.7c16.8 12.2 50.2 41.8 73.4 41.4">
                </path>
              </svg></span>
            <div>
              <h3>Email</h3>
              <p><a
                  href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya"><EMAIL></a>
              </p>
            </div>
          </div>
          <div class="contact-item">
            <span><svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0a256 256 0 1 1 0 512a256 256 0 1 1 0-512m-24 120v136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24">
                </path>
              </svg></span>
            <div>
              <h3>Operating Hours</h3>
              <p>Monday to Friday: 8:00am – 5:00pm 
</p>
            </div>
          </div>
          <!-- Social Media Icons -->
          <div class="contact-item">


            <span><svg class="contact-icon" xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem"
                viewBox="0 0 16 16">
                <path fill="currentColor"
                  d="M12 10c-.8 0-1.4.3-2 .8L6.8 9c.1-.3.2-.7.2-1s-.1-.7-.2-1L10 5.2c.6.5 1.2.8 2 .8c1.7 0 3-1.3 3-3s-1.3-3-3-3s-3 1.3-3 3v.5L5.5 5.4C5.1 5.2 4.6 5 4 5C2.4 5 1 6.3 1 8c0 1.6 1.4 3 3 3c.6 0 1.1-.2 1.5-.4L9 12.5v.5c0 1.7 1.3 3 3 3s3-1.3 3-3s-1.3-3-3-3" />
              </svg></span>

            <div>
              <h3>Connect With Us</h3>


              <div class="social-links">
                <!-- whatsapp -->
               <a href="https://api.whatsapp.com/send/?phone=0710842717&amp;text&amp;type=phone_number&amp;app_absent=0" target="_blank" class="social-link" aria-label="Whatsapp">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" color="currentColor">
                      <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12c0 1.379.28 2.693.784 3.888c.279.66.418.99.436 1.24c.017.25-.057.524-.204 1.073L2 22l3.799-1.016c.549-.147.823-.22 1.073-.204c.25.018.58.157 1.24.436A10 10 0 0 0 12 22"></path>
                      <path d="m8.588 12.377l.871-1.081c.367-.456.82-.88.857-1.488c.008-.153-.1-.841-.315-2.218C9.916 7.049 9.41 7 8.973 7c-.57 0-.855 0-1.138.13c-.358.163-.725.622-.806 1.007c-.064.305-.016.515.079.935c.402 1.783 1.347 3.544 2.811 5.009c1.465 1.464 3.226 2.409 5.01 2.811c.42.095.629.143.934.079c.385-.08.844-.448 1.008-.806c.129-.283.129-.568.129-1.138c0-.438-.049-.943-.59-1.028c-1.377-.216-2.065-.323-2.218-.315c-.607.036-1.032.49-1.488.857l-1.081.87"></path>
                    </g>
                  </svg>
                </a>

              
                <!-- Facebook -->
                <a href="https://www.facebook.com/PrefabMaterialsKenya" target="_blank"
                  class="social-link" aria-label="Facebook">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <path fill="currentColor"
                      d="m16.117 8.906l-2.589 3.086l-3.715-2.281a.5.5 0 0 0-.644.104l-3.052 3.636a.5.5 0 0 0 .766.643l2.774-3.306l3.715 2.281c.211.13.485.085.645-.104l2.866-3.416a.5.5 0 0 0-.766-.643M12 1C5.715 1 .975 5.594.975 11.686a10.43 10.43 0 0 0 3.471 7.905c.071.06.114.149.118.242l.058 1.867a1.343 1.343 0 0 0 1.883 1.187l2.088-.92a.33.33 0 0 1 .226-.018c1.037.283 2.107.425 3.181.422c6.285 0 11.025-4.594 11.025-10.685S18.285 1 12 1m0 20.371a11 11 0 0 1-2.916-.387a1.36 1.36 0 0 0-.894.068l-2.086.919a.35.35 0 0 1-.324-.026a.34.34 0 0 1-.158-.276l-.058-1.871a1.34 1.34 0 0 0-.45-.952a9.45 9.45 0 0 1-3.14-7.16C1.975 6.164 6.285 2 12 2s10.025 4.164 10.025 9.686S17.715 21.37 12 21.37" />
                  </svg>
                </a>
              
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <footer class="footer">
    <div class="content-grid">
      <div class="footer-content">
        <div class="copyright">
          <p>&copy; <span id="current-year"></span> Eco Brain Contractors. All Rights Reserved.</p>

        </div>
        <div class="designer">
          <a href="https://www.yellowpageskenya.com/" target="_blank" rel="noopener noreferrer">
            <img src="./img/yp_logo.webp" loading="lazy" alt="Yellow Pages Kenya" width="50" height="50"
              title="Yellow Pages Kenya">
            <p>Powered by Yellow Pages Kenya.</p>
          </a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Google tag (gtag.js) -->
  <script>
  function loadGTM() {
    if (window.gtmLoaded) return; // Prevent multiple loads
    window.gtmLoaded = true;
 
    var script = document.createElement("script");
    script.src = "https://www.googletagmanager.com/gtag/js?id=G-62YRXTBVE8";
    script.async = true;
    document.head.appendChild(script);
 
    window.dataLayer = window.dataLayer || [];
    function gtag(){ dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'G-62YRXTBVE8');
  }
 
  window.addEventListener("scroll", loadGTM, { once: true });
  window.addEventListener("mousemove", loadGTM, { once: true });
  window.addEventListener("touchstart", loadGTM, { once: true });
</script>

  <script src="./js/testimonial.js"></script>
  <!-- <script src="./js/main.js"></script> -->

  <script src="https://cdn.jsdelivr.net/npm/vanilla-tilt@latest/dist/vanilla-tilt.min.js"></script>



  <script>
    document.getElementById('current-year').textContent = new Date().getFullYear();
  </script>

  <script>
    // Initialize VanillaTilt for 3D card effect
    VanillaTilt.init(document.querySelectorAll(".card"), {
      max: 5,
      speed: 400,
      glare: true,
      "max-glare": 0.2,
    });

    // Intersection Observer for scroll animations
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = 1;
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, {
      threshold: 0.1
    });

    // Observe all cards
    document.querySelectorAll('.card').forEach(card => {
      card.style.opacity = 0;
      card.style.transform = 'translateY(20px)';
      observer.observe(card);
    });
  </script>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const topBar = document.getElementById('top-bar');
      const mainNav = document.getElementById('main-nav');
      const mainContent = document.querySelector('body'); // Adjust this selector if needed
      const mobileMenu = document.getElementById('mobile-menu');
      const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
      const closeMobileMenu = document.getElementById('close-mobile-menu');
      const mobileMenuLinks = document.querySelectorAll('.mobile-menu .menu-links a');

      // Mobile Menu Logic
      mobileMenuToggle.addEventListener('click', () => {
        mobileMenu.classList.add('show');
      });

      closeMobileMenu.addEventListener('click', () => {
        mobileMenu.classList.remove('show');
      });

      // Add click event listeners to all mobile menu links
      mobileMenuLinks.forEach(link => {
        link.addEventListener('click', function (event) {
          mobileMenu.classList.remove('show');

          const href = this.getAttribute('href');
          if (href.startsWith('#') && href !== '#') {
            event.preventDefault();

            const targetElement = document.querySelector(href);

            if (targetElement) {
              setTimeout(() => {
                const yOffset = -80;
                const y = targetElement.getBoundingClientRect().top + window.pageYOffset + yOffset;

                window.scrollTo({
                  top: y,
                  behavior: 'smooth'
                });
              }, 300);
            }
          }
        });
      });

      // Debug function to log sticky state
      function logStickyState() {
        //console.log('Scroll position:', window.scrollY);
        //console.log('mainNav has sticky class:', mainNav.classList.contains('sticky'));
        //console.log('mainNav style:', mainNav.style.cssText);
        //console.log('computed position:', window.getComputedStyle(mainNav).position);
      }

      // Improved Sticky Header Logic
      function handleScroll() {
        const scrollTop = window.scrollY || document.documentElement.scrollTop;
        //console.log('Scrolling, position:', scrollTop);

        if (scrollTop > 50) {
          // Make sure we're applying direct styles
          mainNav.style.position = 'fixed';
          mainNav.style.top = '0';
          mainNav.style.left = '0';
          mainNav.style.width = '100%';
          mainNav.style.zIndex = '100';
          mainNav.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
          mainNav.classList.add('sticky');

          // Add padding to body to prevent content jump
          mainContent.style.paddingTop = mainNav.offsetHeight + 'px';

          // Hide the top bar
          if (topBar) {
            topBar.style.display = 'none';
          }

          logStickyState();
        } else {
          // Remove direct styles
          mainNav.style.position = '';
          mainNav.style.top = '';
          mainNav.style.left = '';
          mainNav.style.width = '';
          mainNav.style.zIndex = '';
          mainNav.style.boxShadow = '';
          mainNav.classList.remove('sticky');

          // Remove padding from body
          mainContent.style.paddingTop = '0';

          // Show the top bar on desktop
          if (topBar && window.innerWidth >= 1024) {
            topBar.style.display = 'block';
          }

          logStickyState();
        }
      }

      // Initial check on page load
      handleScroll();

      // Add scroll event listener
      window.addEventListener('scroll', handleScroll);

      // Handle window resize
      window.addEventListener('resize', () => {
        if (window.innerWidth < 1024 && topBar) {
          topBar.style.display = 'none';
        } else if (window.scrollY <= 50 && topBar) {
          topBar.style.display = 'block';
        }

        // Recalculate sticky state on resize
        handleScroll();
      });
    });
  </script>



  <script>
   
    function throttle(fn, limit) {
      let waiting = false;
      return function (...args) {
        if (!waiting) {
          fn.apply(this, args);
          waiting = true;
          setTimeout(() => waiting = false, limit);
        }
      };
    }

    function handleParallaxScroll() {
      const elements = document.querySelectorAll('[data-parallax]');
      const scrollY = window.scrollY;

      elements.forEach(el => {
        const container = el.closest('.parallax-container');
        const rect = container.getBoundingClientRect();
        const offsetTop = container.offsetTop;
        const height = container.offsetHeight;

        // Only calculate if it's in view
        if (scrollY + window.innerHeight > offsetTop && scrollY < offsetTop + height) {
          const speed = 0.5; // Adjust this to control intensity
          const yPos = (scrollY - offsetTop) * speed;
          el.style.transform = `translateY(${yPos}px)`;
        }
      });
    }

    document.addEventListener('DOMContentLoaded', function () {
      window.addEventListener('scroll', throttle(handleParallaxScroll, 16)); // 60fps-ish
    });
  </script>
  
 <script>
document.addEventListener("DOMContentLoaded", function () {
  const lazySections = document.querySelectorAll('.lazy-background');

  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const el = entry.target;
        const bgUrl = el.dataset.bg;

        // Create a new image to preload the background
        const img = new Image();
        img.src = bgUrl;

        img.onload = function () {
          // Only set background when fully loaded
          el.style.backgroundImage = `url('${bgUrl}')`;
          el.classList.add('loaded');
        };

        // Stop observing this element
        observer.unobserve(el);
      }
    });
  }, {
    rootMargin: '200px', // Preload a bit before the element enters the viewport
    threshold: 0.1
  });

  lazySections.forEach(section => observer.observe(section));
});
</script>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Get all slides
        const slides = document.querySelectorAll('.slide');
        const dots = document.querySelectorAll('.slider-dot');
        const prevArrow = document.querySelector('.arrow.prev');
        const nextArrow = document.querySelector('.arrow.next');
        let currentSlide = 0;
        let slideInterval;
        let isAnimating = false;
        
        // Function to change slide
        function goToSlide(index) {
          if (isAnimating || currentSlide === index) return;
          
          isAnimating = true;
          
          // Remove active class from all slides and dots
          slides.forEach(slide => slide.classList.remove('active'));
          dots.forEach(dot => dot.classList.remove('active'));
          
          // Add active class to current slide and dot
          slides[index].classList.add('active');
          dots[index].classList.add('active');
          
          // Update current slide index
          currentSlide = index;
          
          // Set a timeout to prevent rapid clicking during transition
          setTimeout(() => {
            isAnimating = false;
          }, 1200); // Match this to your CSS transition time
        }
        
        // Function to go to next slide
        function nextSlide() {
          if (isAnimating) return;
          
          let nextIndex = currentSlide + 1;
          if (nextIndex >= slides.length) {
            nextIndex = 0;
          }
          goToSlide(nextIndex);
        }
        
        // Function to go to previous slide
        function prevSlide() {
          if (isAnimating) return;
          
          let prevIndex = currentSlide - 1;
          if (prevIndex < 0) {
            prevIndex = slides.length - 1;
          }
          goToSlide(prevIndex);
        }
        
        // Set up dot click events
        dots.forEach(dot => {
          dot.addEventListener('click', function() {
            if (isAnimating) return;
            goToSlide(parseInt(this.getAttribute('data-index')));
            restartSlideInterval();
          });
        });
        
        // Set up arrow click events
        nextArrow.addEventListener('click', function() {
          nextSlide();
          restartSlideInterval();
        });
        
        prevArrow.addEventListener('click', function() {
          prevSlide();
          restartSlideInterval();
        });
        
        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
          if (e.key === 'ArrowLeft') {
            prevSlide();
            restartSlideInterval();
          } else if (e.key === 'ArrowRight') {
            nextSlide();
            restartSlideInterval();
          }
        });
        
        // Function to start automatic slide transition
        function startSlideInterval() {
          slideInterval = setInterval(nextSlide, 7000); // Change slide every 7 seconds
        }
        
        // Function to restart slide interval after user interaction
        function restartSlideInterval() {
          clearInterval(slideInterval);
          startSlideInterval();
        }
        
        // Pause autoplay on hover
        const sliderContainer = document.querySelector('.hero-slider');
        sliderContainer.addEventListener('mouseenter', function() {
          clearInterval(slideInterval);
        });
        
        sliderContainer.addEventListener('mouseleave', function() {
          startSlideInterval();
        });
        
        // Start automatic slide transition
        startSlideInterval();
      });
    </script> 


</body>

</html>